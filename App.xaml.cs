using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.Sqlite;

namespace MedicalDevicesManager
{
    public partial class App : Application
    {
        public static MedicalDevicesContext DatabaseContext { get; private set; }
        
        protected override async void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            try
            {
                DatabaseContext = new MedicalDevicesContext();

                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                await DatabaseContext.Database.EnsureCreatedAsync();

                // تحديث مخطط قاعدة البيانات
                await UpdateDatabaseSchemaAsync();

                // إضافة البيانات التجريبية
                await SeedDataAsync();

                // التأكد من حفظ التغييرات
                await DatabaseContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}\n\nتفاصيل الخطأ: {ex.InnerException?.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        private async Task UpdateDatabaseSchemaAsync()
        {
            try
            {
                using (var connection = DatabaseContext.Database.GetDbConnection())
                {
                    await connection.OpenAsync();
                    using (var command = connection.CreateCommand())
                    {
                        // إنشاء جدول فئات الأجهزة إذا لم يكن موجوداً
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS DeviceCategories (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                Name TEXT NOT NULL,
                                Description TEXT,
                                Icon TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول فئات المخزون
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS InventoryCategories (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                Name TEXT NOT NULL,
                                Description TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول أنواع العملاء
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS CustomerTypes (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                Name TEXT NOT NULL,
                                Description TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول المدن
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS Cities (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                Name TEXT NOT NULL,
                                Country TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول أسماء المستلمين
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS RecipientNames (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                Name TEXT NOT NULL,
                                Phone TEXT,
                                Address TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول أسماء الفنيين
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS TechnicianNames (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                Name TEXT NOT NULL,
                                Specialization TEXT,
                                Phone TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول الإشعارات والتنبيهات
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS Notifications (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                Title TEXT NOT NULL,
                                Message TEXT NOT NULL,
                                Type TEXT DEFAULT 'Info',
                                Category TEXT,
                                RelatedItemId INTEGER,
                                RelatedItemType TEXT,
                                IsRead INTEGER DEFAULT 0,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                ReadDate TEXT,
                                Priority TEXT DEFAULT 'Medium'
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول النسخ الاحتياطي
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS BackupRecords (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                BackupName TEXT NOT NULL,
                                BackupPath TEXT NOT NULL,
                                BackupType TEXT DEFAULT 'Manual',
                                FileSize INTEGER DEFAULT 0,
                                Status TEXT DEFAULT 'InProgress',
                                Description TEXT,
                                CreatedDate TEXT,
                                CompletedDate TEXT,
                                ErrorMessage TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول تصدير البيانات
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS ExportRecords (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                ExportName TEXT NOT NULL,
                                ExportType TEXT NOT NULL,
                                DataType TEXT NOT NULL,
                                FilePath TEXT NOT NULL,
                                Parameters TEXT,
                                Status TEXT DEFAULT 'InProgress',
                                FileSize INTEGER DEFAULT 0,
                                CreatedDate TEXT,
                                CompletedDate TEXT,
                                CreatedBy TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول إعدادات المخزون المتقدمة
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS InventorySettings (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                InventoryItemId INTEGER NOT NULL,
                                MinimumStock INTEGER DEFAULT 0,
                                ReorderLevel INTEGER DEFAULT 0,
                                MaximumStock INTEGER DEFAULT 0,
                                EnableLowStockAlert INTEGER DEFAULT 1,
                                EnableExpiryAlert INTEGER DEFAULT 1,
                                ExpiryAlertDays INTEGER DEFAULT 30,
                                AlertEmail TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT,
                                FOREIGN KEY (InventoryItemId) REFERENCES InventoryItems (Id)
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول الأرقام التسلسلية للأجهزة الطبية
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS DeviceSerialNumbers (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                DeviceId INTEGER NOT NULL,
                                SerialNumber TEXT NOT NULL,
                                ComponentName TEXT NOT NULL,
                                ComponentType TEXT DEFAULT 'رئيسي',
                                Description TEXT,
                                Location TEXT,
                                Status TEXT DEFAULT 'نشط',
                                InstallationDate TEXT,
                                LastMaintenanceDate TEXT,
                                Notes TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT,
                                FOREIGN KEY (DeviceId) REFERENCES MedicalDevices (Id)
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول إدارة تنصيب الأجهزة الطبية
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS DeviceInstallations (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                MedicalDeviceId INTEGER NOT NULL,
                                DeviceName TEXT NOT NULL,
                                SerialNumber TEXT NOT NULL,
                                Model TEXT,
                                Manufacturer TEXT,
                                CustomerId INTEGER NOT NULL,
                                CustomerName TEXT NOT NULL,
                                InstallationDate TEXT NOT NULL,
                                WarrantyYears INTEGER DEFAULT 1,
                                WarrantyEndDate TEXT,
                                InstallationLocation TEXT,
                                ContactPersonName TEXT,
                                ContactPersonPhone TEXT,
                                ContactPersonEmail TEXT,
                                ContactPersonPosition TEXT,
                                InstallationCost REAL DEFAULT 0,
                                InstallationStatus TEXT DEFAULT 'مكتمل',
                                TechnicianName TEXT,
                                InstallationNotes TEXT,
                                EquipmentCondition TEXT DEFAULT 'ممتاز',
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT,
                                FOREIGN KEY (MedicalDeviceId) REFERENCES MedicalDevices (Id),
                                FOREIGN KEY (CustomerId) REFERENCES Customers (Id)
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول التنبيهات الذكية المتقدمة
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS SmartAlerts (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                AlertType TEXT NOT NULL,
                                Title TEXT NOT NULL,
                                Message TEXT NOT NULL,
                                Priority TEXT DEFAULT 'متوسط',
                                Status TEXT DEFAULT 'نشط',
                                RelatedItemId INTEGER,
                                RelatedItemType TEXT,
                                RelatedItemName TEXT,
                                AlertDate TEXT NOT NULL,
                                DueDate TEXT,
                                CompletedDate TEXT,
                                ActionRequired TEXT,
                                AssignedTo TEXT,
                                IsAutoGenerated INTEGER DEFAULT 1,
                                IsRecurring INTEGER DEFAULT 0,
                                RecurringDays INTEGER,
                                NextAlertDate TEXT,
                                Category TEXT,
                                Tags TEXT,
                                EstimatedCost REAL,
                                Notes TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول إعدادات التنبيهات
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS AlertSettings (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                AlertType TEXT NOT NULL,
                                SettingName TEXT NOT NULL,
                                SettingValue TEXT NOT NULL,
                                Description TEXT,
                                IsEnabled INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول البحث الذكي والفهرسة
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS SearchIndexes (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                ItemType TEXT NOT NULL,
                                ItemId INTEGER NOT NULL,
                                SearchableText TEXT NOT NULL,
                                Keywords TEXT,
                                Category TEXT,
                                NumericValue1 REAL,
                                NumericValue2 REAL,
                                DateValue1 TEXT,
                                DateValue2 TEXT,
                                Status TEXT,
                                Tags TEXT,
                                SearchCount INTEGER DEFAULT 0,
                                LastSearched TEXT,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إنشاء جدول الإحصائيات المباشرة
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS LiveStatistics (
                                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                StatType TEXT NOT NULL,
                                StatName TEXT NOT NULL,
                                CurrentValue REAL NOT NULL,
                                PreviousValue REAL DEFAULT 0,
                                ChangeValue REAL DEFAULT 0,
                                ChangePercentage REAL DEFAULT 0,
                                Trend TEXT DEFAULT 'stable',
                                Period TEXT DEFAULT 'daily',
                                CalculatedDate TEXT NOT NULL,
                                AdditionalData TEXT,
                                IsActive INTEGER DEFAULT 1,
                                CreatedDate TEXT,
                                LastUpdated TEXT
                            )";
                        await command.ExecuteNonQueryAsync();

                        // إضافة أعمدة المستندات إلى جدول الأجهزة إذا لم تكن موجودة
                        var columnsToAdd = new[]
                        {
                            "UserManualPath TEXT DEFAULT ''",
                            "MaintenanceManualPath TEXT DEFAULT ''",
                            "OriginCertificatePath TEXT DEFAULT ''",
                            "OfficialCertificationsPath TEXT DEFAULT ''"
                        };

                        foreach (var column in columnsToAdd)
                        {
                            try
                            {
                                command.CommandText = $"ALTER TABLE MedicalDevices ADD COLUMN {column}";
                                await command.ExecuteNonQueryAsync();
                            }
                            catch
                            {
                                // العمود موجود بالفعل، تجاهل الخطأ
                            }
                        }

                        // إضافة عمود PaymentMethod إلى جدول Sales إذا لم يكن موجوداً
                        try
                        {
                            command.CommandText = "ALTER TABLE Sales ADD COLUMN PaymentMethod TEXT DEFAULT 'نقدي'";
                            await command.ExecuteNonQueryAsync();
                        }
                        catch
                        {
                            // العمود موجود بالفعل، تجاهل الخطأ
                        }

                        // إضافة عمود NextMaintenanceDate إلى جدول MaintenanceRecords إذا لم يكن موجوداً
                        try
                        {
                            command.CommandText = "ALTER TABLE MaintenanceRecords ADD COLUMN NextMaintenanceDate TEXT";
                            await command.ExecuteNonQueryAsync();
                        }
                        catch
                        {
                            // العمود موجود بالفعل، تجاهل الخطأ
                        }

                        // إدراج الفئات الافتراضية إذا لم تكن موجودة
                        command.CommandText = "SELECT COUNT(*) FROM DeviceCategories";
                        var count = Convert.ToInt32(await command.ExecuteScalarAsync());

                        if (count == 0)
                        {
                            var defaultCategories = new[]
                            {
                                ("أجهزة الأشعة", "أجهزة التصوير الطبي والأشعة السينية", "📡"),
                                ("أجهزة المختبر", "أجهزة التحليل والفحوصات المخبرية", "🔬"),
                                ("أجهزة القلب", "أجهزة فحص وعلاج أمراض القلب", "❤️"),
                                ("أجهزة الفحص", "أجهزة الفحص الطبي العام", "🩺"),
                                ("أجهزة الجراحة", "أدوات ومعدات العمليات الجراحية", "🔪"),
                                ("أجهزة العناية المركزة", "معدات وحدات العناية المركزة", "🏥"),
                                ("أجهزة الأعصاب", "أجهزة فحص وعلاج الجهاز العصبي", "🧠"),
                                ("أجهزة العيون", "معدات فحص وعلاج العيون", "👁️")
                            };

                            foreach (var (name, description, icon) in defaultCategories)
                            {
                                command.CommandText = @"
                                    INSERT INTO DeviceCategories (Name, Description, Icon, IsActive, CreatedDate, LastUpdated)
                                    VALUES (@name, @description, @icon, 1, @date, @date)";
                                command.Parameters.Clear();
                                command.Parameters.Add(new Microsoft.Data.Sqlite.SqliteParameter("@name", name));
                                command.Parameters.Add(new Microsoft.Data.Sqlite.SqliteParameter("@description", description));
                                command.Parameters.Add(new Microsoft.Data.Sqlite.SqliteParameter("@icon", icon));
                                command.Parameters.Add(new Microsoft.Data.Sqlite.SqliteParameter("@date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                                await command.ExecuteNonQueryAsync();
                            }
                        }

                        // إدراج فئات المخزون الافتراضية
                        command.CommandText = "SELECT COUNT(*) FROM InventoryCategories";
                        var inventoryCategoriesCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                        if (inventoryCategoriesCount == 0)
                        {
                            var inventoryCategories = new[]
                            {
                                ("مستلزمات طبية", "المستلزمات الطبية العامة"),
                                ("أدوات طبية", "الأدوات والمعدات الطبية"),
                                ("مواد التعقيم", "مواد التطهير والتعقيم"),
                                ("أدوات مختبر", "معدات وأدوات المختبرات"),
                                ("أدوات جراحية", "الأدوات الجراحية والعمليات"),
                                ("مستلزمات العناية", "مستلزمات العناية والتمريض"),
                                ("أدوية ومستحضرات", "الأدوية والمستحضرات الطبية"),
                                ("مواد استهلاكية", "المواد الاستهلاكية الطبية")
                            };

                            foreach (var (name, description) in inventoryCategories)
                            {
                                command.CommandText = @"
                                    INSERT INTO InventoryCategories (Name, Description, IsActive, CreatedDate, LastUpdated)
                                    VALUES (@name, @description, 1, @date, @date)";
                                command.Parameters.Clear();
                                command.Parameters.Add(new SqliteParameter("@name", name));
                                command.Parameters.Add(new SqliteParameter("@description", description));
                                command.Parameters.Add(new SqliteParameter("@date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                                await command.ExecuteNonQueryAsync();
                            }
                        }

                        // إدراج أنواع العملاء الافتراضية
                        command.CommandText = "SELECT COUNT(*) FROM CustomerTypes";
                        var customerTypesCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                        if (customerTypesCount == 0)
                        {
                            var customerTypes = new[]
                            {
                                ("مستشفى حكومي", "المستشفيات والمراكز الحكومية"),
                                ("مستشفى خاص", "المستشفيات والعيادات الخاصة"),
                                ("عيادة طبية", "العيادات الطبية المتخصصة"),
                                ("مركز طبي", "المراكز الطبية والتشخيصية"),
                                ("صيدلية", "الصيدليات ومراكز الأدوية"),
                                ("مختبر طبي", "المختبرات الطبية والتحليلية"),
                                ("عميل فردي", "العملاء الأفراد"),
                                ("موزع طبي", "الموزعين والوكلاء الطبيين")
                            };

                            foreach (var (name, description) in customerTypes)
                            {
                                command.CommandText = @"
                                    INSERT INTO CustomerTypes (Name, Description, IsActive, CreatedDate, LastUpdated)
                                    VALUES (@name, @description, 1, @date, @date)";
                                command.Parameters.Clear();
                                command.Parameters.Add(new SqliteParameter("@name", name));
                                command.Parameters.Add(new SqliteParameter("@description", description));
                                command.Parameters.Add(new SqliteParameter("@date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                                await command.ExecuteNonQueryAsync();
                            }
                        }

                        // إدراج المدن الافتراضية
                        command.CommandText = "SELECT COUNT(*) FROM Cities";
                        var citiesCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                        if (citiesCount == 0)
                        {
                            var cities = new[]
                            {
                                // المدن العراقية
                                ("بغداد", "العراق"), ("البصرة", "العراق"), ("الموصل", "العراق"), ("أربيل", "العراق"),
                                ("النجف", "العراق"), ("كربلاء", "العراق"), ("السليمانية", "العراق"), ("الأنبار", "العراق"),
                                ("ديالى", "العراق"), ("بابل", "العراق"), ("نينوى", "العراق"), ("دهوك", "العراق"),
                                ("كركوك", "العراق"), ("واسط", "العراق"), ("صلاح الدين", "العراق"), ("المثنى", "العراق"),
                                ("القادسية", "العراق"), ("ذي قار", "العراق"), ("ميسان", "العراق"),

                                // المدن العربية الرئيسية
                                ("الرياض", "السعودية"), ("جدة", "السعودية"), ("مكة المكرمة", "السعودية"), ("المدينة المنورة", "السعودية"),
                                ("الدمام", "السعودية"), ("الكويت", "الكويت"), ("الدوحة", "قطر"), ("المنامة", "البحرين"),
                                ("أبو ظبي", "الإمارات"), ("دبي", "الإمارات"), ("مسقط", "عمان"), ("عمان", "الأردن"),
                                ("دمشق", "سوريا"), ("حلب", "سوريا"), ("بيروت", "لبنان"), ("القاهرة", "مصر"),
                                ("الإسكندرية", "مصر"), ("طرابلس", "ليبيا"), ("بنغازي", "ليبيا"), ("تونس", "تونس"),
                                ("الجزائر", "الجزائر"), ("الرباط", "المغرب"), ("الدار البيضاء", "المغرب"), ("صنعاء", "اليمن")
                            };

                            foreach (var (name, country) in cities)
                            {
                                command.CommandText = @"
                                    INSERT INTO Cities (Name, Country, IsActive, CreatedDate, LastUpdated)
                                    VALUES (@name, @country, 1, @date, @date)";
                                command.Parameters.Clear();
                                command.Parameters.Add(new SqliteParameter("@name", name));
                                command.Parameters.Add(new SqliteParameter("@country", country));
                                command.Parameters.Add(new SqliteParameter("@date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                                await command.ExecuteNonQueryAsync();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async Task SeedDataAsync()
        {
            try
            {
                if (!await DatabaseContext.MedicalDevices.AnyAsync())
            {
                var devices = new List<MedicalDevice>
                {
                    new MedicalDevice
                    {
                        Name = "جهاز الأشعة السينية المحمول",
                        Brand = "Philips",
                        Model = "MobileXR-100",
                        SerialNumber = "PH001",
                        Category = "أجهزة الأشعة",
                        Description = "جهاز أشعة سينية محمول عالي الجودة",
                        PurchasePrice = 150000,
                        SellingPrice = 180000,
                        PurchaseDate = DateTime.Now.AddDays(-30),
                        WarrantyStartDate = DateTime.Now.AddDays(-30),
                        WarrantyEndDate = DateTime.Now.AddYears(2),
                        Supplier = "شركة فيليبس الطبية",
                        Location = "المستودع الرئيسي",
                        Status = "متاح",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new MedicalDevice
                    {
                        Name = "جهاز الموجات فوق الصوتية",
                        Brand = "GE Healthcare",
                        Model = "LOGIQ-E10",
                        SerialNumber = "GE002",
                        Category = "أجهزة التصوير",
                        Description = "جهاز موجات فوق صوتية متطور",
                        PurchasePrice = 200000,
                        SellingPrice = 240000,
                        PurchaseDate = DateTime.Now.AddDays(-45),
                        WarrantyStartDate = DateTime.Now.AddDays(-45),
                        WarrantyEndDate = DateTime.Now.AddYears(3),
                        Supplier = "جنرال إلكتريك الطبية",
                        Location = "المستودع الرئيسي",
                        Status = "متاح",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new MedicalDevice
                    {
                        Name = "جهاز تخطيط القلب",
                        Brand = "Schiller",
                        Model = "AT-102",
                        SerialNumber = "SC003",
                        Category = "أجهزة القلب",
                        Description = "جهاز تخطيط قلب رقمي",
                        PurchasePrice = 25000,
                        SellingPrice = 30000,
                        PurchaseDate = DateTime.Now.AddDays(-20),
                        WarrantyStartDate = DateTime.Now.AddDays(-20),
                        WarrantyEndDate = DateTime.Now.AddYears(1),
                        Supplier = "شيلر الطبية",
                        Location = "المستودع الثانوي",
                        Status = "متاح",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new MedicalDevice
                    {
                        Name = "جهاز قياس ضغط الدم الرقمي",
                        Brand = "Omron",
                        Model = "HEM-7120",
                        SerialNumber = "OM004",
                        Category = "أجهزة القياس",
                        Description = "جهاز قياس ضغط دم رقمي دقيق",
                        PurchasePrice = 500,
                        SellingPrice = 650,
                        PurchaseDate = DateTime.Now.AddDays(-10),
                        WarrantyStartDate = DateTime.Now.AddDays(-10),
                        WarrantyEndDate = DateTime.Now.AddYears(1),
                        Supplier = "أومرون الطبية",
                        Location = "المستودع الرئيسي",
                        Status = "متاح",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new MedicalDevice
                    {
                        Name = "جهاز التنفس الصناعي",
                        Brand = "Medtronic",
                        Model = "PB980",
                        SerialNumber = "MD005",
                        Category = "أجهزة التنفس",
                        Description = "جهاز تنفس صناعي متطور",
                        PurchasePrice = 80000,
                        SellingPrice = 95000,
                        PurchaseDate = DateTime.Now.AddDays(-60),
                        WarrantyStartDate = DateTime.Now.AddDays(-60),
                        WarrantyEndDate = DateTime.Now.AddYears(2),
                        Supplier = "مدترونيك الطبية",
                        Location = "المستودع الرئيسي",
                        Status = "مباع",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    }
                };
                
                DatabaseContext.MedicalDevices.AddRange(devices);
                
                var inventoryItems = new List<InventoryItem>
                {
                    new InventoryItem
                    {
                        Name = "قفازات طبية",
                        Category = "مستلزمات طبية",
                        Description = "قفازات طبية معقمة",
                        CurrentStock = 500,
                        MinimumStock = 100,
                        Unit = "صندوق",
                        UnitPrice = 25,
                        Location = "المستودع الرئيسي",
                        Status = "متاح",
                        ExpiryDate = DateTime.Now.AddYears(2),
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new InventoryItem
                    {
                        Name = "كمامات جراحية",
                        Category = "مستلزمات طبية",
                        Description = "كمامات جراحية ثلاثية الطبقات",
                        CurrentStock = 1000,
                        MinimumStock = 200,
                        Unit = "صندوق",
                        UnitPrice = 15,
                        Location = "المستودع الرئيسي",
                        Status = "متاح",
                        ExpiryDate = DateTime.Now.AddYears(3),
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new InventoryItem
                    {
                        Name = "محاقن طبية",
                        Category = "أدوات طبية",
                        Description = "محاقن طبية معقمة",
                        CurrentStock = 200,
                        MinimumStock = 50,
                        Unit = "صندوق",
                        UnitPrice = 35,
                        Location = "المستودع الثانوي",
                        Status = "متاح",
                        ExpiryDate = DateTime.Now.AddYears(5),
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new InventoryItem
                    {
                        Name = "شاش طبي معقم",
                        Category = "مستلزمات طبية",
                        Description = "شاش طبي معقم للجروح",
                        CurrentStock = 300,
                        MinimumStock = 80,
                        Unit = "علبة",
                        UnitPrice = 12,
                        Location = "المستودع الرئيسي",
                        Status = "متاح",
                        ExpiryDate = DateTime.Now.AddYears(3),
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new InventoryItem
                    {
                        Name = "مطهر طبي",
                        Category = "مواد التعقيم",
                        Description = "مطهر طبي للأيدي والأسطح",
                        CurrentStock = 45,
                        MinimumStock = 50,
                        Unit = "زجاجة",
                        UnitPrice = 18,
                        Location = "المستودع الرئيسي",
                        Status = "مخزون منخفض",
                        ExpiryDate = DateTime.Now.AddYears(2),
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new InventoryItem
                    {
                        Name = "أنابيب اختبار",
                        Category = "أدوات مختبر",
                        Description = "أنابيب اختبار معقمة للعينات",
                        CurrentStock = 800,
                        MinimumStock = 200,
                        Unit = "علبة",
                        UnitPrice = 45,
                        Location = "المستودع الثانوي",
                        Status = "متاح",
                        ExpiryDate = DateTime.Now.AddYears(4),
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new InventoryItem
                    {
                        Name = "ضمادات لاصقة",
                        Category = "مستلزمات طبية",
                        Description = "ضمادات لاصقة مختلفة الأحجام",
                        CurrentStock = 150,
                        MinimumStock = 100,
                        Unit = "صندوق",
                        UnitPrice = 22,
                        Location = "المستودع الرئيسي",
                        Status = "متاح",
                        ExpiryDate = DateTime.Now.AddYears(3),
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new InventoryItem
                    {
                        Name = "قطن طبي",
                        Category = "مستلزمات طبية",
                        Description = "قطن طبي معقم",
                        CurrentStock = 25,
                        MinimumStock = 30,
                        Unit = "كيس",
                        UnitPrice = 8,
                        Location = "المستودع الثانوي",
                        Status = "مخزون منخفض",
                        ExpiryDate = DateTime.Now.AddYears(5),
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    }
                };
                
                DatabaseContext.InventoryItems.AddRange(inventoryItems);
                
                var customers = new List<Customer>
                {
                    new Customer
                    {
                        Name = "مستشفى الملك فهد",
                        CustomerType = "مستشفى",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "شارع الملك فهد، الرياض",
                        City = "الرياض",
                        PostalCode = "11564",
                        CreditLimit = 500000,
                        Status = "نشط",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Customer
                    {
                        Name = "عيادات المملكة الطبية",
                        CustomerType = "عيادة",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "حي العليا، الرياض",
                        City = "الرياض",
                        PostalCode = "12345",
                        CreditLimit = 100000,
                        Status = "نشط",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Customer
                    {
                        Name = "مجمع الدمام الطبي",
                        CustomerType = "مجمع طبي",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "شارع الملك عبدالعزيز، الدمام",
                        City = "الدمام",
                        PostalCode = "31411",
                        CreditLimit = 300000,
                        Status = "نشط",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Customer
                    {
                        Name = "مستوصف النور الطبي",
                        CustomerType = "مستوصف",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "حي الفيصلية، جدة",
                        City = "جدة",
                        PostalCode = "21432",
                        CreditLimit = 75000,
                        Status = "نشط",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Customer
                    {
                        Name = "مركز الأسنان المتخصص",
                        CustomerType = "مركز متخصص",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "حي الملز، الرياض",
                        City = "الرياض",
                        PostalCode = "11663",
                        CreditLimit = 50000,
                        Status = "معلق",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    }
                };
                
                DatabaseContext.Customers.AddRange(customers);
                
                var suppliers = new List<Supplier>
                {
                    new Supplier
                    {
                        Name = "شركة فيليبس الطبية",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "برج المملكة، الرياض",
                        City = "الرياض",
                        PostalCode = "11564",
                        Rating = 4.8m,
                        Status = "نشط",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Supplier
                    {
                        Name = "جنرال إلكتريك الطبية",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "مركز الملك عبدالله المالي، الرياض",
                        City = "الرياض",
                        PostalCode = "13519",
                        Rating = 4.7m,
                        Status = "نشط",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Supplier
                    {
                        Name = "شيلر الطبية السعودية",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "حي الصحافة، الرياض",
                        City = "الرياض",
                        PostalCode = "13315",
                        Rating = 4.5m,
                        Status = "نشط",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Supplier
                    {
                        Name = "أومرون الشرق الأوسط",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "مجمع دبي التجاري، دبي",
                        City = "دبي",
                        PostalCode = "00000",
                        Rating = 4.6m,
                        Status = "نشط",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Supplier
                    {
                        Name = "مدترونيك العربية",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "مدينة الملك عبدالله الاقتصادية، جدة",
                        City = "جدة",
                        PostalCode = "23964",
                        Rating = 4.9m,
                        Status = "نشط",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Supplier
                    {
                        Name = "الشركة الطبية المتقدمة",
                        Phone = "************",
                        Email = "<EMAIL>",
                        Address = "طريق الملك فهد، الرياض",
                        City = "الرياض",
                        PostalCode = "11432",
                        Rating = 4.2m,
                        Status = "معلق",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    }
                };
                
                DatabaseContext.Suppliers.AddRange(suppliers);
                
                var sales = new List<Sale>
                {
                    new Sale
                    {
                        InvoiceNumber = "INV-2025-001",
                        SaleDate = DateTime.Now.AddDays(-5),
                        CustomerId = 1,
                        CustomerName = "مستشفى الملك فهد",
                        MedicalDeviceId = 1,
                        DeviceName = "جهاز الأشعة السينية المحمول",
                        Quantity = 1,
                        UnitPrice = 180000,
                        TotalAmount = 180000,
                        Discount = 5000,
                        FinalAmount = 175000,
                        PaymentStatus = "مدفوع",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Sale
                    {
                        InvoiceNumber = "INV-2025-002",
                        SaleDate = DateTime.Now.AddDays(-3),
                        CustomerId = 2,
                        CustomerName = "عيادات المملكة الطبية",
                        MedicalDeviceId = 4,
                        DeviceName = "جهاز قياس ضغط الدم الرقمي",
                        Quantity = 5,
                        UnitPrice = 650,
                        TotalAmount = 3250,
                        Discount = 250,
                        FinalAmount = 3000,
                        PaymentStatus = "مدفوع",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Sale
                    {
                        InvoiceNumber = "INV-2025-003",
                        SaleDate = DateTime.Now.AddDays(-1),
                        CustomerId = 1,
                        CustomerName = "مستشفى الملك فهد",
                        MedicalDeviceId = 5,
                        DeviceName = "جهاز التنفس الصناعي",
                        Quantity = 1,
                        UnitPrice = 95000,
                        TotalAmount = 95000,
                        Discount = 0,
                        FinalAmount = 95000,
                        PaymentStatus = "مؤجل",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    }
                };

                DatabaseContext.Sales.AddRange(sales);

                var shipments = new List<Shipment>
                {
                    new Shipment
                    {
                        ShipmentNumber = "SHP-2025-001",
                        ShipmentDate = DateTime.Now.AddDays(-4),
                        RecipientName = "مستشفى الملك فهد",
                        DeliveryAddress = "شارع الملك فهد، الرياض 11564",
                        Status = "وصلت",
                        TotalValue = 175000,
                        ShippingCost = 500,
                        TrackingNumber = "TRK001234567",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Shipment
                    {
                        ShipmentNumber = "SHP-2025-002",
                        ShipmentDate = DateTime.Now.AddDays(-2),
                        RecipientName = "عيادات المملكة الطبية",
                        DeliveryAddress = "حي العليا، الرياض 12345",
                        Status = "في الطريق",
                        TotalValue = 3000,
                        ShippingCost = 100,
                        TrackingNumber = "TRK001234568",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new Shipment
                    {
                        ShipmentNumber = "SHP-2025-003",
                        ShipmentDate = DateTime.Now,
                        RecipientName = "مستشفى الملك فهد",
                        DeliveryAddress = "شارع الملك فهد، الرياض 11564",
                        Status = "قيد التحضير",
                        TotalValue = 95000,
                        ShippingCost = 800,
                        TrackingNumber = "TRK001234569",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    }
                };

                DatabaseContext.Shipments.AddRange(shipments);

                var maintenanceRecords = new List<MaintenanceRecord>
                {
                    new MaintenanceRecord
                    {
                        MedicalDeviceId = 1,
                        DeviceName = "جهاز الأشعة السينية المحمول",
                        MaintenanceType = "صيانة دورية",
                        MaintenanceDate = DateTime.Now.AddDays(-15),
                        Status = "مكتملة",
                        Cost = 2500,
                        TechnicianName = "أحمد محمد",
                        Description = "فحص شامل وتنظيف الجهاز",
                        NextMaintenanceDate = DateTime.Now.AddDays(75), // الصيانة التالية بعد 3 أشهر
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new MaintenanceRecord
                    {
                        MedicalDeviceId = 2,
                        DeviceName = "جهاز الموجات فوق الصوتية",
                        MaintenanceType = "إصلاح",
                        MaintenanceDate = DateTime.Now.AddDays(-8),
                        Status = "مكتملة",
                        Cost = 1800,
                        TechnicianName = "سعد العلي",
                        Description = "إصلاح مشكلة في الشاشة",
                        NextMaintenanceDate = DateTime.Now.AddDays(60), // الصيانة التالية بعد شهرين
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new MaintenanceRecord
                    {
                        MedicalDeviceId = 3,
                        DeviceName = "جهاز تخطيط القلب",
                        MaintenanceType = "صيانة دورية",
                        MaintenanceDate = DateTime.Now.AddDays(7),
                        Status = "مجدولة",
                        Cost = 800,
                        TechnicianName = "محمد الأحمد",
                        Description = "صيانة دورية مجدولة",
                        NextMaintenanceDate = DateTime.Now.AddDays(97), // الصيانة التالية بعد 3 أشهر
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    },
                    new MaintenanceRecord
                    {
                        MedicalDeviceId = 5,
                        DeviceName = "جهاز التنفس الصناعي",
                        MaintenanceType = "فحص ضمان",
                        MaintenanceDate = DateTime.Now.AddDays(15),
                        Status = "مجدولة",
                        Cost = 0,
                        TechnicianName = "خالد السعد",
                        Description = "فحص ضمان سنوي",
                        NextMaintenanceDate = DateTime.Now.AddDays(380), // الصيانة التالية بعد سنة
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    }
                };

                DatabaseContext.MaintenanceRecords.AddRange(maintenanceRecords);

                // حفظ التغييرات مع معالجة الأخطاء
                await DatabaseContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء البيانات التجريبية: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        protected override void OnExit(ExitEventArgs e)
        {
            DatabaseContext?.Dispose();
            base.OnExit(e);
        }
    }
}
