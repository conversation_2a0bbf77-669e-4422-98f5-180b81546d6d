﻿#pragma checksum "..\..\..\..\Windows\DeviceDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EBC5290E8FD5E49AB375034E419945371DC1E3B6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// DeviceDetailsWindow
    /// </summary>
    public partial class DeviceDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialNumberTextBlock;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SalesCountSummary;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceCountSummary;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InstallationsCountSummary;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilesCountSummary;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BrandTextBlock;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModelTextBlock;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoryTextBlock;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DescriptionTextBlock;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PurchasePriceTextBlock;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SellingPriceTextBlock;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierTextBlock;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LocationTextBlock;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PurchaseDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarrantyStartTextBlock;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarrantyEndTextBlock;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SpecificationsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManageSerialNumbersBtn;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SerialNumbersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SerialNumbersCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserManualTextBlock;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenUserManualBtn;
        
        #line default
        #line hidden
        
        
        #line 320 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceManualTextBlock;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenMaintenanceManualBtn;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OriginCertificateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOriginCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OfficialCertificationsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOfficialCertificationsBtn;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SalesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SalesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid MaintenanceDataGrid;
        
        #line default
        #line hidden
        
        
        #line 428 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 445 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InstallationsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 462 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InstallationsCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 499 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserManualFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 501 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenUserManualFileBtn;
        
        #line default
        #line hidden
        
        
        #line 507 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaintenanceManualFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 509 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenMaintenanceManualFileBtn;
        
        #line default
        #line hidden
        
        
        #line 515 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OriginCertificateFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 517 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOriginCertificateFileBtn;
        
        #line default
        #line hidden
        
        
        #line 523 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OfficialCertificationsFileTextBlock;
        
        #line default
        #line hidden
        
        
        #line 525 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOfficialCertificationsFileBtn;
        
        #line default
        #line hidden
        
        
        #line 533 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportBookletsPanel;
        
        #line default
        #line hidden
        
        
        #line 541 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AdditionalFilesPanel;
        
        #line default
        #line hidden
        
        
        #line 550 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 566 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditDeviceBtn;
        
        #line default
        #line hidden
        
        
        #line 570 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintDetailsBtn;
        
        #line default
        #line hidden
        
        
        #line 574 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/devicedetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DeviceNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SerialNumberTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.SalesCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.MaintenanceCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.InstallationsCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.FilesCountSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.BrandTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ModelTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CategoryTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.DescriptionTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PurchasePriceTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.SellingPriceTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.SupplierTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.LocationTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.PurchaseDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.CreatedDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.WarrantyStartTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.WarrantyEndTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.NotesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.SpecificationsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.ManageSerialNumbersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.ManageSerialNumbersBtn.Click += new System.Windows.RoutedEventHandler(this.ManageSerialNumbersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.SerialNumbersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 24:
            this.SerialNumbersCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.UserManualTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.OpenUserManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 307 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenUserManualBtn.Click += new System.Windows.RoutedEventHandler(this.OpenUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.MaintenanceManualTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.OpenMaintenanceManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 324 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenMaintenanceManualBtn.Click += new System.Windows.RoutedEventHandler(this.OpenMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.OriginCertificateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.OpenOriginCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 341 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOriginCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.OfficialCertificationsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.OpenOfficialCertificationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 358 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOfficialCertificationsBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.SalesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 34:
            this.SalesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.MaintenanceDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 36:
            this.MaintenanceCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.InstallationsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 38:
            this.InstallationsCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.UserManualFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.OpenUserManualFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 502 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenUserManualFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.MaintenanceManualFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.OpenMaintenanceManualFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 510 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenMaintenanceManualFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.OriginCertificateFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 44:
            this.OpenOriginCertificateFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 518 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOriginCertificateFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.OfficialCertificationsFileTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 46:
            this.OpenOfficialCertificationsFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 526 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.OpenOfficialCertificationsFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            this.ReportBookletsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 48:
            this.AdditionalFilesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 49:
            this.FilesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.EditDeviceBtn = ((System.Windows.Controls.Button)(target));
            
            #line 568 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.EditDeviceBtn.Click += new System.Windows.RoutedEventHandler(this.EditDeviceBtn_Click);
            
            #line default
            #line hidden
            return;
            case 51:
            this.PrintDetailsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 572 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.PrintDetailsBtn.Click += new System.Windows.RoutedEventHandler(this.PrintDetailsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 52:
            this.CloseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 576 "..\..\..\..\Windows\DeviceDetailsWindow.xaml"
            this.CloseBtn.Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

