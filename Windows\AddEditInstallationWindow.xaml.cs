using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using System.IO;
using System.Diagnostics;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditInstallationWindow : Window
    {
        private DeviceInstallation _installation;
        private bool _isEditMode;
        private List<MedicalDevice> _devices;
        private List<Customer> _customers;

        public AddEditInstallationWindow(DeviceInstallation installation = null)
        {
            InitializeComponent();
            _installation = installation;
            _isEditMode = installation != null;
            
            if (_isEditMode)
            {
                TitleTextBlock.Text = "🔧 تعديل تنصيب الجهاز";
                Title = "تعديل تنصيب الجهاز";
            }
            
            LoadDataAsync();
        }

        private async void LoadDataAsync()
        {
            try
            {
                // تحميل الأجهزة المتاحة
                _devices = await App.DatabaseContext.MedicalDevices
                    .Where(d => d.Status == "متاح" || (_isEditMode && d.Id == _installation.MedicalDeviceId))
                    .ToListAsync();
                
                DeviceComboBox.ItemsSource = _devices;
                DeviceComboBox.DisplayMemberPath = "Name";
                DeviceComboBox.SelectedValuePath = "Id";

                // تحميل العملاء
                _customers = await App.DatabaseContext.Customers
                    .Where(c => c.Status == "نشط")
                    .ToListAsync();
                
                CustomerComboBox.ItemsSource = _customers;
                CustomerComboBox.DisplayMemberPath = "Name";
                CustomerComboBox.SelectedValuePath = "Id";

                // تعيين القيم الافتراضية
                InstallationDatePicker.SelectedDate = DateTime.Now;
                WarrantyYearsTextBox.Text = "1";
                InstallationStatusComboBox.SelectedIndex = 0; // مكتمل
                EquipmentConditionComboBox.SelectedIndex = 0; // ممتاز

                // إذا كان في وضع التعديل، تحميل البيانات
                if (_isEditMode && _installation != null)
                {
                    LoadInstallationData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadInstallationData()
        {
            if (_installation == null) return;

            DeviceComboBox.SelectedValue = _installation.MedicalDeviceId;
            CustomerComboBox.SelectedValue = _installation.CustomerId;
            InstallationDatePicker.SelectedDate = _installation.InstallationDate;
            WarrantyYearsTextBox.Text = _installation.WarrantyYears.ToString();
            WarrantyEndDatePicker.SelectedDate = _installation.WarrantyEndDate;
            InstallationLocationTextBox.Text = _installation.InstallationLocation;
            ContactPersonNameTextBox.Text = _installation.ContactPersonName;
            ContactPersonPhoneTextBox.Text = _installation.ContactPersonPhone;
            ContactPersonEmailTextBox.Text = _installation.ContactPersonEmail;
            ContactPersonPositionTextBox.Text = _installation.ContactPersonPosition;
            InstallationCostTextBox.Text = _installation.InstallationCost.ToString();

            // تحميل كتيب التقرير
            if (!string.IsNullOrEmpty(_installation.InstallationReportBookletPath))
            {
                InstallationReportPathTextBox.Text = Path.GetFileName(_installation.InstallationReportBookletPath);
                InstallationReportPathTextBox.Tag = _installation.InstallationReportBookletPath;
                ViewInstallationReportBtn.IsEnabled = File.Exists(_installation.InstallationReportBookletPath);
                RemoveInstallationReportBtn.IsEnabled = true;
            }

            // تحميل الأرقام التسلسلية وتحديد الرقم المحفوظ
            if (_installation.MedicalDeviceId.HasValue)
            {
                await LoadSerialNumbersForDevice(_installation.MedicalDeviceId.Value);

                // تحديد الرقم التسلسلي المحفوظ
                if (!string.IsNullOrEmpty(_installation.SerialNumber))
                {
                    SerialNumberComboBox.SelectedValue = _installation.SerialNumber;
                }
            }
            TechnicianNameTextBox.Text = _installation.TechnicianName;
            InstallationNotesTextBox.Text = _installation.InstallationNotes;

            // تعيين حالة التنصيب
            foreach (ComboBoxItem item in InstallationStatusComboBox.Items)
            {
                if (item.Content.ToString() == _installation.InstallationStatus)
                {
                    InstallationStatusComboBox.SelectedItem = item;
                    break;
                }
            }

            // تعيين حالة المعدات
            foreach (ComboBoxItem item in EquipmentConditionComboBox.Items)
            {
                if (item.Content.ToString() == _installation.EquipmentCondition)
                {
                    EquipmentConditionComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private async void DeviceComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DeviceComboBox.SelectedItem is MedicalDevice selectedDevice)
            {
                ModelTextBox.Text = selectedDevice.Model;
                ManufacturerTextBox.Text = selectedDevice.Brand;

                // تحميل الأرقام التسلسلية للجهاز المحدد
                await LoadSerialNumbersForDevice(selectedDevice.Id);
            }
        }

        private async System.Threading.Tasks.Task LoadSerialNumbersForDevice(int deviceId)
        {
            try
            {
                var serialNumbers = await App.DatabaseContext.DeviceSerialNumbers
                    .Where(s => s.DeviceId == deviceId && s.IsActive && s.Status == "نشط")
                    .OrderBy(s => s.ComponentName)
                    .ToListAsync();

                SerialNumberComboBox.ItemsSource = serialNumbers;

                if (serialNumbers.Any())
                {
                    // اختيار أول رقم تسلسلي متاح
                    SerialNumberComboBox.SelectedIndex = 0;
                }
                else
                {
                    // إذا لم توجد أرقام تسلسلية، استخدم الرقم التسلسلي الأساسي للجهاز
                    var device = await App.DatabaseContext.MedicalDevices
                        .FirstOrDefaultAsync(d => d.Id == deviceId);

                    if (device != null && !string.IsNullOrEmpty(device.SerialNumber))
                    {
                        var basicSerial = new DeviceSerialNumber
                        {
                            SerialNumber = device.SerialNumber,
                            ComponentName = "الجهاز الأساسي",
                            ComponentType = "رئيسي"
                        };

                        SerialNumberComboBox.ItemsSource = new List<DeviceSerialNumber> { basicSerial };
                        SerialNumberComboBox.SelectedIndex = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأرقام التسلسلية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SerialNumberComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        }

        private void WarrantyYearsTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (int.TryParse(WarrantyYearsTextBox.Text, out int years) &&
                InstallationDatePicker.SelectedDate.HasValue)
            {
                WarrantyEndDatePicker.SelectedDate = InstallationDatePicker.SelectedDate.Value.AddYears(years);
            }
        }

        private void WarrantyEndDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق للتحقق من صحة تاريخ انتهاء الضمان
            if (WarrantyEndDatePicker.SelectedDate.HasValue && InstallationDatePicker.SelectedDate.HasValue)
            {
                if (WarrantyEndDatePicker.SelectedDate < InstallationDatePicker.SelectedDate)
                {
                    MessageBox.Show("تاريخ انتهاء الضمان لا يمكن أن يكون قبل تاريخ التنصيب", "خطأ في التاريخ",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    WarrantyEndDatePicker.SelectedDate = InstallationDatePicker.SelectedDate.Value.AddYears(1);
                }
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                var selectedDevice = DeviceComboBox.SelectedItem as MedicalDevice;
                var selectedCustomer = CustomerComboBox.SelectedItem as Customer;

                if (_isEditMode)
                {
                    // تحديث التنصيب الموجود
                    _installation.MedicalDeviceId = selectedDevice.Id;
                    _installation.DeviceName = selectedDevice.Name;
                    _installation.SerialNumber = SerialNumberComboBox.SelectedValue?.ToString() ?? selectedDevice.SerialNumber;
                    _installation.Model = selectedDevice.Model;
                    _installation.Manufacturer = selectedDevice.Brand;
                    _installation.CustomerId = selectedCustomer.Id;
                    _installation.CustomerName = selectedCustomer.Name;
                    _installation.InstallationDate = InstallationDatePicker.SelectedDate.Value;
                    _installation.WarrantyYears = int.Parse(WarrantyYearsTextBox.Text);
                    _installation.WarrantyEndDate = WarrantyEndDatePicker.SelectedDate.Value;
                    _installation.InstallationLocation = InstallationLocationTextBox.Text;
                    _installation.ContactPersonName = ContactPersonNameTextBox.Text;
                    _installation.ContactPersonPhone = ContactPersonPhoneTextBox.Text;
                    _installation.ContactPersonEmail = ContactPersonEmailTextBox.Text;
                    _installation.ContactPersonPosition = ContactPersonPositionTextBox.Text;
                    _installation.InstallationCost = decimal.Parse(InstallationCostTextBox.Text);
                    _installation.InstallationStatus = ((ComboBoxItem)InstallationStatusComboBox.SelectedItem).Content.ToString();
                    _installation.TechnicianName = TechnicianNameTextBox.Text;
                    _installation.InstallationNotes = InstallationNotesTextBox.Text;
                    _installation.EquipmentCondition = ((ComboBoxItem)EquipmentConditionComboBox.SelectedItem).Content.ToString();

                    // حفظ مسار كتيب التقرير
                    if (InstallationReportPathTextBox.Text != "لم يتم اختيار ملف")
                    {
                        _installation.InstallationReportBookletPath = InstallationReportPathTextBox.Tag?.ToString() ?? "";
                    }

                    _installation.LastUpdated = DateTime.Now;

                    App.DatabaseContext.DeviceInstallations.Update(_installation);
                }
                else
                {
                    // إنشاء تنصيب جديد
                    var newInstallation = new DeviceInstallation
                    {
                        MedicalDeviceId = selectedDevice.Id,
                        DeviceName = selectedDevice.Name,
                        SerialNumber = SerialNumberComboBox.SelectedValue?.ToString() ?? selectedDevice.SerialNumber,
                        Model = selectedDevice.Model,
                        Manufacturer = selectedDevice.Brand,
                        CustomerId = selectedCustomer.Id,
                        CustomerName = selectedCustomer.Name,
                        InstallationDate = InstallationDatePicker.SelectedDate.Value,
                        WarrantyYears = int.Parse(WarrantyYearsTextBox.Text),
                        WarrantyEndDate = WarrantyEndDatePicker.SelectedDate.Value,
                        InstallationLocation = InstallationLocationTextBox.Text,
                        ContactPersonName = ContactPersonNameTextBox.Text,
                        ContactPersonPhone = ContactPersonPhoneTextBox.Text,
                        ContactPersonEmail = ContactPersonEmailTextBox.Text,
                        ContactPersonPosition = ContactPersonPositionTextBox.Text,
                        InstallationCost = decimal.Parse(InstallationCostTextBox.Text),
                        InstallationStatus = ((ComboBoxItem)InstallationStatusComboBox.SelectedItem).Content.ToString(),
                        TechnicianName = TechnicianNameTextBox.Text,
                        InstallationNotes = InstallationNotesTextBox.Text,
                        EquipmentCondition = ((ComboBoxItem)EquipmentConditionComboBox.SelectedItem).Content.ToString(),
                        InstallationReportBookletPath = InstallationReportPathTextBox.Text != "لم يتم اختيار ملف" ?
                                                       InstallationReportPathTextBox.Tag?.ToString() ?? "" : "",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };

                    App.DatabaseContext.DeviceInstallations.Add(newInstallation);
                }

                await App.DatabaseContext.SaveChangesAsync();

                MessageBox.Show(
                    _isEditMode ? "تم تحديث التنصيب بنجاح!" : "تم إضافة التنصيب بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التنصيب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (DeviceComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الجهاز الطبي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                DeviceComboBox.Focus();
                return false;
            }

            if (CustomerComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار العميل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CustomerComboBox.Focus();
                return false;
            }

            if (!InstallationDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى تحديد تاريخ التنصيب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                InstallationDatePicker.Focus();
                return false;
            }

            if (!int.TryParse(WarrantyYearsTextBox.Text, out int warrantyYears) || warrantyYears < 0)
            {
                MessageBox.Show("يرجى إدخال عدد سنوات الضمان بشكل صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                WarrantyYearsTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(InstallationCostTextBox.Text, out decimal cost) || cost < 0)
            {
                MessageBox.Show("يرجى إدخال تكلفة التنصيب بشكل صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                InstallationCostTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(InstallationLocationTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال موقع التنصيب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                InstallationLocationTextBox.Focus();
                return false;
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void BrowseInstallationReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Title = "اختيار كتيب تقرير التنصيب",
                    Filter = "PDF Files|*.pdf|Word Documents|*.doc;*.docx|Image Files|*.jpg;*.jpeg;*.png;*.bmp|All Files|*.*",
                    FilterIndex = 1
                };

                if (openDialog.ShowDialog() == true)
                {
                    var fileName = openDialog.FileName;
                    var fileInfo = new FileInfo(fileName);

                    // التحقق من حجم الملف (أقل من 10 ميجابايت)
                    if (fileInfo.Length > 10 * 1024 * 1024)
                    {
                        MessageBox.Show("حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 10 ميجابايت.",
                                      "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // نسخ الملف إلى مجلد التطبيق
                    var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                                  "MedicalDevicesManager", "InstallationReports");
                    Directory.CreateDirectory(appDataPath);

                    var newFileName = $"Installation_{DateTime.Now:yyyyMMdd_HHmmss}_{Path.GetFileName(fileName)}";
                    var destinationPath = Path.Combine(appDataPath, newFileName);

                    File.Copy(fileName, destinationPath, true);

                    InstallationReportPathTextBox.Text = Path.GetFileName(destinationPath);
                    InstallationReportPathTextBox.Tag = destinationPath;
                    ViewInstallationReportBtn.IsEnabled = true;
                    RemoveInstallationReportBtn.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewInstallationReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = InstallationReportPathTextBox.Tag?.ToString();
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود أو تم حذفه.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ResetInstallationReportBooklet();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveInstallationReportBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد حذف كتيب التقرير؟", "تأكيد الحذف",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var filePath = InstallationReportPathTextBox.Tag?.ToString();
                    if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }
                catch (Exception ex)
                {
                    // تجاهل أخطاء حذف الملف
                    System.Diagnostics.Debug.WriteLine($"Error deleting file: {ex.Message}");
                }

                ResetInstallationReportBooklet();
            }
        }

        private void ResetInstallationReportBooklet()
        {
            InstallationReportPathTextBox.Text = "لم يتم اختيار ملف";
            InstallationReportPathTextBox.Tag = null;
            ViewInstallationReportBtn.IsEnabled = false;
            RemoveInstallationReportBtn.IsEnabled = false;
        }
    }
}
