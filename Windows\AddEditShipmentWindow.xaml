<Window x:Class="MedicalDevicesManager.Windows.AddEditShipmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل شحنة" Height="650" Width="600"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" x:Name="TitleBlock" Text="إضافة شحنة جديدة" 
                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                   Margin="0,0,0,20" Foreground="#007BFF"/>
        
        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- رقم الشحنة -->
                <TextBlock Text="رقم الشحنة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="ShipmentNumberTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15" IsReadOnly="True"/>
                
                <!-- تاريخ الشحن -->
                <TextBlock Text="تاريخ الشحن *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <DatePicker x:Name="ShipmentDatePicker" Height="35" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- اسم المستلم -->
                <TextBlock Text="اسم المستلم *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox Grid.Column="0" x:Name="RecipientComboBox" Height="35" Padding="10,8" FontSize="14"
                              IsEditable="True" Margin="0,0,5,0"
                              DisplayMemberPath="Name" SelectedValuePath="Name" SelectionChanged="RecipientComboBox_SelectionChanged"/>

                    <Button Grid.Column="1" x:Name="ManageRecipientsBtn" Content="⚙️" Width="35" Height="35"
                            Background="#6C757D" Foreground="White" BorderThickness="0"
                            ToolTip="إدارة أسماء المستلمين" Click="ManageRecipientsBtn_Click"/>
                </Grid>
                
                <!-- عنوان التسليم -->
                <TextBlock Text="عنوان التسليم *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="DeliveryAddressTextBox" Height="60" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- الحالة -->
                <TextBlock Text="حالة الشحنة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="StatusComboBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15">
                    <ComboBoxItem Content="قيد التحضير"/>
                    <ComboBoxItem Content="جاهزة للشحن"/>
                    <ComboBoxItem Content="في الطريق"/>
                    <ComboBoxItem Content="وصلت"/>
                    <ComboBoxItem Content="تم التسليم"/>
                    <ComboBoxItem Content="مؤجلة"/>
                    <ComboBoxItem Content="ملغية"/>
                </ComboBox>
                
                <!-- القيم المالية -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="قيمة الشحنة *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TotalValueTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="تكلفة الشحن *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ShippingCostTextBox" Height="35" Padding="10,8" FontSize="14"/>
                    </StackPanel>
                </Grid>
                
                <!-- رقم التتبع -->
                <TextBlock Text="رقم التتبع" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="TrackingNumberTextBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15"/>
                
                <!-- شركة الشحن -->
                <TextBlock Text="شركة الشحن" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox x:Name="ShippingCompanyComboBox" Height="35" Padding="10,8" FontSize="14" Margin="0,0,0,15">
                    <ComboBoxItem Content="شركة البريد السعودي"/>
                    <ComboBoxItem Content="شركة أرامكس"/>
                    <ComboBoxItem Content="شركة DHL"/>
                    <ComboBoxItem Content="شركة فيدكس"/>
                    <ComboBoxItem Content="شركة UPS"/>
                    <ComboBoxItem Content="شركة سمسا"/>
                    <ComboBoxItem Content="شركة الجزيرة للشحن"/>
                    <ComboBoxItem Content="أخرى"/>
                </ComboBox>
                
                <!-- ملاحظات -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="NotesTextBox" Height="80" Padding="10,8" FontSize="14" 
                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                
                <!-- معلومات إضافية -->
                <Border Background="#F8F9FA" Padding="15" CornerRadius="5" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="معلومات إضافية" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <TextBlock x:Name="RecipientInfoTextBlock" Text="اختر مستلماً لعرض المعلومات" FontSize="12" Foreground="#6C757D"/>
                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="تاريخ الإنشاء:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="CreatedDateTextBlock" Text="سيتم تعيينه تلقائياً" FontSize="12" FontWeight="SemiBold"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="آخر تحديث:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="LastUpdatedTextBlock" Text="سيتم تعيينه تلقائياً" FontSize="12" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="💾 حفظ الشحنة" Width="140" Height="40" 
                    Background="#28A745" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" Width="120" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
