using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using System.IO;
using System.Diagnostics;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditMaintenanceWindow : Window
    {
        private MaintenanceRecord _maintenance;
        private bool _isEditMode;
        
        public AddEditMaintenanceWindow(MaintenanceRecord maintenance = null)
        {
            InitializeComponent();
            _maintenance = maintenance;
            _isEditMode = maintenance != null;
            
            LoadDataAsync();
            LoadTechniciansAsync();
            
            if (_isEditMode)
            {
                TitleBlock.Text = "تعديل سجل صيانة";
                LoadMaintenanceData();
            }
            else
            {
                // تعيين القيم الافتراضية
                MaintenanceDatePicker.SelectedDate = DateTime.Now;
                MaintenanceTypeComboBox.SelectedIndex = 0;
                StatusComboBox.SelectedIndex = 0;
                TechnicianComboBox.SelectedIndex = 0;
                CostTextBox.Text = "0";
                CreatedDateTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
                LastUpdatedTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
            }
        }
        
        private async void LoadDataAsync()
        {
            try
            {
                // تحميل الأجهزة الطبية
                var devices = await App.DatabaseContext.MedicalDevices.ToListAsync();
                DeviceComboBox.ItemsSource = devices;
                
                if (!_isEditMode && devices.Any())
                    DeviceComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void LoadMaintenanceData()
        {
            if (_maintenance == null) return;

            MaintenanceDatePicker.SelectedDate = _maintenance.MaintenanceDate;
            CostTextBox.Text = _maintenance.Cost.ToString();
            DescriptionTextBox.Text = _maintenance.Description;
            NotesTextBox.Text = _maintenance.Notes;
            CreatedDateTextBlock.Text = _maintenance.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            LastUpdatedTextBlock.Text = _maintenance.LastUpdated.ToString("dd/MM/yyyy HH:mm");

            // تحميل كتيب التقرير
            if (!string.IsNullOrEmpty(_maintenance.ReportBookletPath))
            {
                ReportBookletPathTextBox.Text = Path.GetFileName(_maintenance.ReportBookletPath);
                ViewReportBtn.IsEnabled = File.Exists(_maintenance.ReportBookletPath);
                RemoveReportBtn.IsEnabled = true;
            }

            // تعيين الجهاز
            DeviceComboBox.SelectedValue = _maintenance.MedicalDeviceId;

            // تعيين نوع الصيانة
            foreach (ComboBoxItem item in MaintenanceTypeComboBox.Items)
            {
                if (item.Content.ToString() == _maintenance.MaintenanceType)
                {
                    MaintenanceTypeComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الحالة
            foreach (ComboBoxItem item in StatusComboBox.Items)
            {
                if (item.Content.ToString() == _maintenance.Status)
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الفني
            foreach (ComboBoxItem item in TechnicianComboBox.Items)
            {
                if (item.Content.ToString() == _maintenance.TechnicianName)
                {
                    TechnicianComboBox.SelectedItem = item;
                    break;
                }
            }
        }
        
        private void DeviceComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DeviceComboBox.SelectedItem is MedicalDevice device)
            {
                DeviceInfoTextBlock.Text = $"الجهاز: {device.Name} - الماركة: {device.Brand} - الموديل: {device.Model} - الحالة: {device.Status}";
            }
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;
            
            try
            {
                if (_isEditMode)
                {
                    UpdateMaintenanceData();
                    App.DatabaseContext.MaintenanceRecords.Update(_maintenance);
                }
                else
                {
                    _maintenance = new MaintenanceRecord();
                    UpdateMaintenanceData();
                    _maintenance.CreatedDate = DateTime.Now;
                    App.DatabaseContext.MaintenanceRecords.Add(_maintenance);
                }
                
                _maintenance.LastUpdated = DateTime.Now;
                await App.DatabaseContext.SaveChangesAsync();
                
                MessageBox.Show(
                    _isEditMode ? "تم تحديث سجل الصيانة بنجاح!" : "تم إضافة سجل الصيانة بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateMaintenanceData()
        {
            var device = DeviceComboBox.SelectedItem as MedicalDevice;

            _maintenance.MedicalDeviceId = device.Id;
            _maintenance.DeviceName = device.Name;
            _maintenance.MaintenanceType = ((ComboBoxItem)MaintenanceTypeComboBox.SelectedItem).Content.ToString();
            _maintenance.MaintenanceDate = MaintenanceDatePicker.SelectedDate ?? DateTime.Now;
            _maintenance.Status = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
            _maintenance.Cost = decimal.Parse(CostTextBox.Text);
            _maintenance.TechnicianName = ((ComboBoxItem)TechnicianComboBox.SelectedItem).Content.ToString();
            _maintenance.Description = DescriptionTextBox.Text.Trim();
            _maintenance.Notes = NotesTextBox.Text.Trim();

            // حفظ مسار كتيب التقرير
            if (ReportBookletPathTextBox.Text != "لم يتم اختيار ملف")
            {
                _maintenance.ReportBookletPath = ReportBookletPathTextBox.Tag?.ToString() ?? "";
            }
        }
        
        private bool ValidateInput()
        {
            if (DeviceComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الجهاز الطبي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (MaintenanceTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الصيانة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (MaintenanceDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ الصيانة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة الصيانة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(CostTextBox.Text, out decimal cost) || cost < 0)
            {
                MessageBox.Show("يرجى إدخال تكلفة صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (TechnicianComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الفني", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال وصف الصيانة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void BrowseReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Title = "اختيار كتيب تقرير الصيانة",
                    Filter = "PDF Files|*.pdf|Word Documents|*.doc;*.docx|Image Files|*.jpg;*.jpeg;*.png;*.bmp|All Files|*.*",
                    FilterIndex = 1
                };

                if (openDialog.ShowDialog() == true)
                {
                    var fileName = openDialog.FileName;
                    var fileInfo = new FileInfo(fileName);

                    // التحقق من حجم الملف (أقل من 10 ميجابايت)
                    if (fileInfo.Length > 10 * 1024 * 1024)
                    {
                        MessageBox.Show("حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 10 ميجابايت.",
                                      "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // نسخ الملف إلى مجلد التطبيق
                    var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                                                  "MedicalDevicesManager", "MaintenanceReports");
                    Directory.CreateDirectory(appDataPath);

                    var newFileName = $"Maintenance_{DateTime.Now:yyyyMMdd_HHmmss}_{Path.GetFileName(fileName)}";
                    var destinationPath = Path.Combine(appDataPath, newFileName);

                    File.Copy(fileName, destinationPath, true);

                    ReportBookletPathTextBox.Text = Path.GetFileName(destinationPath);
                    ReportBookletPathTextBox.Tag = destinationPath;
                    ViewReportBtn.IsEnabled = true;
                    RemoveReportBtn.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewReportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = ReportBookletPathTextBox.Tag?.ToString();
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود أو تم حذفه.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ResetReportBooklet();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveReportBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد حذف كتيب التقرير؟", "تأكيد الحذف",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var filePath = ReportBookletPathTextBox.Tag?.ToString();
                    if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }
                catch (Exception ex)
                {
                    // تجاهل أخطاء حذف الملف
                    System.Diagnostics.Debug.WriteLine($"Error deleting file: {ex.Message}");
                }

                ResetReportBooklet();
            }
        }

        private void ResetReportBooklet()
        {
            ReportBookletPathTextBox.Text = "لم يتم اختيار ملف";
            ReportBookletPathTextBox.Tag = null;
            ViewReportBtn.IsEnabled = false;
            RemoveReportBtn.IsEnabled = false;
        }

        private async void LoadTechniciansAsync()
        {
            try
            {
                var technicians = await App.DatabaseContext.TechnicianNames
                    .Where(t => t.IsActive)
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                TechnicianComboBox.ItemsSource = technicians;

                // إضافة فنيين افتراضيين إذا لم يوجدوا
                if (!technicians.Any())
                {
                    var defaultTechnicians = new[]
                    {
                        new TechnicianName { Name = "أحمد محمد", Specialization = "صيانة عامة", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "سعد العلي", Specialization = "أجهزة طبية", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "محمد الأحمد", Specialization = "أجهزة تشخيص", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "خالد السعد", Specialization = "أجهزة جراحية", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "عبدالله الحسن", Specialization = "أجهزة مختبر", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now },
                        new TechnicianName { Name = "فني خارجي", Specialization = "متخصص", Phone = "", IsActive = true, CreatedDate = DateTime.Now, LastUpdated = DateTime.Now }
                    };

                    App.DatabaseContext.TechnicianNames.AddRange(defaultTechnicians);
                    await App.DatabaseContext.SaveChangesAsync();

                    TechnicianComboBox.ItemsSource = defaultTechnicians;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أسماء الفنيين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ManageTechniciansBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageTechniciansWindow = new ManageTechniciansWindow();
            if (manageTechniciansWindow.ShowDialog() == true)
            {
                LoadTechniciansAsync();
            }
        }
    }
}
