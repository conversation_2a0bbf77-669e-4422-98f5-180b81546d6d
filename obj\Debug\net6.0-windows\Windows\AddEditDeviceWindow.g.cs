﻿#pragma checksum "..\..\..\..\Windows\AddEditDeviceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EFBC0D94128AA0BD91FEC20D0919B5C35810F3D0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// AddEditDeviceWindow
    /// </summary>
    public partial class AddEditDeviceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 15 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleBlock;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BrandTextBox;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ModelTextBox;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SerialNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManageSerialNumbersBtn;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManageCategoriesBtn;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PurchasePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SellingPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker PurchaseDatePicker;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker WarrantyStartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker WarrantyEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SupplierComboBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LocationComboBox;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UserManualPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseUserManualBtn;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearUserManualBtn;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaintenanceManualPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseMaintenanceManualBtn;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearMaintenanceManualBtn;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OriginCertificatePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseOriginCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearOriginCertificateBtn;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficialCertificationsPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseOfficialCertificationsBtn;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearOfficialCertificationsBtn;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/addeditdevicewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.BrandTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.ModelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.SerialNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.ManageSerialNumbersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ManageSerialNumbersBtn.Click += new System.Windows.RoutedEventHandler(this.ManageSerialNumbersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.ManageCategoriesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 64 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ManageCategoriesBtn.Click += new System.Windows.RoutedEventHandler(this.ManageCategoriesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.PurchasePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.SellingPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.PurchaseDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 13:
            this.WarrantyStartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 14:
            this.WarrantyEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 15:
            this.SupplierComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.LocationComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 17:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.UserManualPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.BrowseUserManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseUserManualBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.ClearUserManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearUserManualBtn.Click += new System.Windows.RoutedEventHandler(this.ClearUserManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.MaintenanceManualPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.BrowseMaintenanceManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseMaintenanceManualBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.ClearMaintenanceManualBtn = ((System.Windows.Controls.Button)(target));
            
            #line 189 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearMaintenanceManualBtn.Click += new System.Windows.RoutedEventHandler(this.ClearMaintenanceManualBtn_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.OriginCertificatePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.BrowseOriginCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseOriginCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.ClearOriginCertificateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 209 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearOriginCertificateBtn.Click += new System.Windows.RoutedEventHandler(this.ClearOriginCertificateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.OfficialCertificationsPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            this.BrowseOfficialCertificationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 226 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.BrowseOfficialCertificationsBtn.Click += new System.Windows.RoutedEventHandler(this.BrowseOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.ClearOfficialCertificationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 229 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.ClearOfficialCertificationsBtn.Click += new System.Windows.RoutedEventHandler(this.ClearOfficialCertificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 240 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 243 "..\..\..\..\Windows\AddEditDeviceWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

