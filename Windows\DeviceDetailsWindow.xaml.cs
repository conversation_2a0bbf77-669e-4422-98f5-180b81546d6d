using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class DeviceDetailsWindow : Window
    {
        private MedicalDevice _device;
        private List<DeviceSerialNumber> _serialNumbers;
        private List<Sale> _deviceSales;
        private List<MaintenanceRecord> _deviceMaintenance;
        private List<DeviceInstallation> _deviceInstallations;

        public DeviceDetailsWindow(MedicalDevice device)
        {
            InitializeComponent();
            _device = device;
            LoadDeviceDetailsAsync();
        }
        
        private async void LoadDeviceDetailsAsync()
        {
            try
            {
                // تحميل المعلومات الأساسية
                LoadBasicInfo();

                // تحميل الأرقام التسلسلية
                await LoadSerialNumbersAsync();

                // تحميل معلومات المستندات
                LoadDocumentsInfo();

                // تحميل معلومات الملفات في التبويب الجديد
                LoadFilesInfo();

                // تحميل البيانات المرتبطة
                await LoadRelatedDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void LoadBasicInfo()
        {
            // العنوان
            DeviceNameTextBlock.Text = _device.Name;
            SerialNumberTextBlock.Text = _device.SerialNumber;
            Title = $"تفاصيل الجهاز - {_device.Name}";
            
            // المعلومات الأساسية
            BrandTextBlock.Text = _device.Brand;
            ModelTextBlock.Text = _device.Model;
            CategoryTextBlock.Text = _device.Category;
            StatusTextBlock.Text = _device.Status;
            DescriptionTextBlock.Text = string.IsNullOrEmpty(_device.Description) ? "لا يوجد وصف" : _device.Description;
            
            // المعلومات المالية
            PurchasePriceTextBlock.Text = $"{_device.PurchasePrice:N0} د.ع";
            SellingPriceTextBlock.Text = $"{_device.SellingPrice:N0} د.ع";
            SupplierTextBlock.Text = string.IsNullOrEmpty(_device.Supplier) ? "غير محدد" : _device.Supplier;
            LocationTextBlock.Text = string.IsNullOrEmpty(_device.Location) ? "غير محدد" : _device.Location;
            
            // التواريخ
            PurchaseDateTextBlock.Text = _device.PurchaseDate.ToString("dd/MM/yyyy");
            CreatedDateTextBlock.Text = _device.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            WarrantyStartTextBlock.Text = _device.WarrantyStartDate.ToString("dd/MM/yyyy");
            WarrantyEndTextBlock.Text = _device.WarrantyEndDate.ToString("dd/MM/yyyy");
            
            // معلومات إضافية
            NotesTextBlock.Text = "لا توجد ملاحظات إضافية";
            SpecificationsTextBlock.Text = string.IsNullOrEmpty(_device.Description) ? "لا توجد مواصفات محددة" : _device.Description;
        }
        
        private async System.Threading.Tasks.Task LoadSerialNumbersAsync()
        {
            try
            {
                _serialNumbers = await App.DatabaseContext.DeviceSerialNumbers
                    .Where(s => s.DeviceId == _device.Id && s.IsActive)
                    .OrderBy(s => s.ComponentName)
                    .ToListAsync();
                
                SerialNumbersDataGrid.ItemsSource = _serialNumbers;
                
                // تحديث عداد الأرقام التسلسلية
                var activeCount = _serialNumbers.Count(s => s.Status == "نشط");
                var totalCount = _serialNumbers.Count;
                SerialNumbersCountTextBlock.Text = $"إجمالي المكونات: {totalCount} | النشطة: {activeCount}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأرقام التسلسلية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void LoadDocumentsInfo()
        {
            // كتيب المستخدم
            if (!string.IsNullOrEmpty(_device.UserManualPath) && File.Exists(_device.UserManualPath))
            {
                UserManualTextBlock.Text = Path.GetFileName(_device.UserManualPath);
                UserManualTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenUserManualBtn.IsEnabled = true;
            }
            
            // كتيب الصيانة
            if (!string.IsNullOrEmpty(_device.MaintenanceManualPath) && File.Exists(_device.MaintenanceManualPath))
            {
                MaintenanceManualTextBlock.Text = Path.GetFileName(_device.MaintenanceManualPath);
                MaintenanceManualTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenMaintenanceManualBtn.IsEnabled = true;
            }
            
            // شهادة المنشأ
            if (!string.IsNullOrEmpty(_device.OriginCertificatePath) && File.Exists(_device.OriginCertificatePath))
            {
                OriginCertificateTextBlock.Text = Path.GetFileName(_device.OriginCertificatePath);
                OriginCertificateTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenOriginCertificateBtn.IsEnabled = true;
            }
            
            // المصادقات الرسمية
            if (!string.IsNullOrEmpty(_device.OfficialCertificationsPath) && File.Exists(_device.OfficialCertificationsPath))
            {
                OfficialCertificationsTextBlock.Text = Path.GetFileName(_device.OfficialCertificationsPath);
                OfficialCertificationsTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                OpenOfficialCertificationsBtn.IsEnabled = true;
            }
        }

        private void LoadFilesInfo()
        {
            try
            {
                int filesCount = 0;

                // كتيب المستخدم
                if (!string.IsNullOrEmpty(_device.UserManualPath) && File.Exists(_device.UserManualPath))
                {
                    UserManualFileTextBlock.Text = Path.GetFileName(_device.UserManualPath);
                    UserManualFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenUserManualFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    UserManualFileTextBlock.Text = "لم يتم رفع ملف";
                    UserManualFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenUserManualFileBtn.IsEnabled = false;
                }

                // كتيب الصيانة
                if (!string.IsNullOrEmpty(_device.MaintenanceManualPath) && File.Exists(_device.MaintenanceManualPath))
                {
                    MaintenanceManualFileTextBlock.Text = Path.GetFileName(_device.MaintenanceManualPath);
                    MaintenanceManualFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenMaintenanceManualFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    MaintenanceManualFileTextBlock.Text = "لم يتم رفع ملف";
                    MaintenanceManualFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenMaintenanceManualFileBtn.IsEnabled = false;
                }

                // شهادة المنشأ
                if (!string.IsNullOrEmpty(_device.OriginCertificatePath) && File.Exists(_device.OriginCertificatePath))
                {
                    OriginCertificateFileTextBlock.Text = Path.GetFileName(_device.OriginCertificatePath);
                    OriginCertificateFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenOriginCertificateFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    OriginCertificateFileTextBlock.Text = "لم يتم رفع ملف";
                    OriginCertificateFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenOriginCertificateFileBtn.IsEnabled = false;
                }

                // المصادقات الرسمية
                if (!string.IsNullOrEmpty(_device.OfficialCertificationsPath) && File.Exists(_device.OfficialCertificationsPath))
                {
                    OfficialCertificationsFileTextBlock.Text = Path.GetFileName(_device.OfficialCertificationsPath);
                    OfficialCertificationsFileTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                    OpenOfficialCertificationsFileBtn.IsEnabled = true;
                    filesCount++;
                }
                else
                {
                    OfficialCertificationsFileTextBlock.Text = "لم يتم رفع ملف";
                    OfficialCertificationsFileTextBlock.Foreground = System.Windows.Media.Brushes.Gray;
                    OpenOfficialCertificationsFileBtn.IsEnabled = false;
                }

                // تحديث عداد الملفات
                FilesCountTextBlock.Text = $"إجمالي الملفات المرفقة: {filesCount} من 4";

                // تحديث الملخص السريع
                FilesCountSummary.Text = $"{filesCount} ملفات";

                // تحميل كتيبات التقارير
                LoadReportBooklets();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات الملفات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadReportBooklets()
        {
            try
            {
                ReportBookletsPanel.Children.Clear();

                // تحميل كتيبات تقارير الصيانة
                var maintenanceReports = await App.DatabaseContext.MaintenanceRecords
                    .Where(m => (m.DeviceId == _device.Id || m.MedicalDeviceId == _device.Id) &&
                               !string.IsNullOrEmpty(m.ReportBookletPath))
                    .ToListAsync();

                foreach (var report in maintenanceReports)
                {
                    if (File.Exists(report.ReportBookletPath))
                    {
                        var reportPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };

                        var reportIcon = new TextBlock { Text = "📋", FontSize = 16, Margin = new Thickness(0, 0, 10, 0) };
                        var reportText = new TextBlock
                        {
                            Text = $"تقرير صيانة - {report.MaintenanceDate:dd/MM/yyyy} - {Path.GetFileName(report.ReportBookletPath)}",
                            VerticalAlignment = VerticalAlignment.Center,
                            Margin = new Thickness(0, 0, 10, 0)
                        };
                        var openButton = new Button
                        {
                            Content = "فتح",
                            Width = 60,
                            Height = 25,
                            Background = System.Windows.Media.Brushes.LightBlue,
                            Tag = report.ReportBookletPath
                        };
                        openButton.Click += (s, e) => OpenDocument(openButton.Tag.ToString());

                        reportPanel.Children.Add(reportIcon);
                        reportPanel.Children.Add(reportText);
                        reportPanel.Children.Add(openButton);

                        ReportBookletsPanel.Children.Add(reportPanel);
                    }
                }

                // تحميل كتيبات تقارير التنصيب
                var installationReports = await App.DatabaseContext.DeviceInstallations
                    .Where(i => i.MedicalDeviceId == _device.Id &&
                               !string.IsNullOrEmpty(i.InstallationReportBookletPath))
                    .ToListAsync();

                foreach (var report in installationReports)
                {
                    if (File.Exists(report.InstallationReportBookletPath))
                    {
                        var reportPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };

                        var reportIcon = new TextBlock { Text = "⚙️", FontSize = 16, Margin = new Thickness(0, 0, 10, 0) };
                        var reportText = new TextBlock
                        {
                            Text = $"تقرير تنصيب - {report.InstallationDate:dd/MM/yyyy} - {Path.GetFileName(report.InstallationReportBookletPath)}",
                            VerticalAlignment = VerticalAlignment.Center,
                            Margin = new Thickness(0, 0, 10, 0)
                        };
                        var openButton = new Button
                        {
                            Content = "فتح",
                            Width = 60,
                            Height = 25,
                            Background = System.Windows.Media.Brushes.LightGreen,
                            Tag = report.InstallationReportBookletPath
                        };
                        openButton.Click += (s, e) => OpenDocument(openButton.Tag.ToString());

                        reportPanel.Children.Add(reportIcon);
                        reportPanel.Children.Add(reportText);
                        reportPanel.Children.Add(openButton);

                        ReportBookletsPanel.Children.Add(reportPanel);
                    }
                }

                if (ReportBookletsPanel.Children.Count == 0)
                {
                    ReportBookletsPanel.Children.Add(new TextBlock
                    {
                        Text = "لا توجد كتيبات تقارير مرفقة حالياً",
                        Foreground = System.Windows.Media.Brushes.Gray,
                        FontStyle = FontStyles.Italic
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل كتيبات التقارير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadRelatedDataAsync()
        {
            try
            {
                // تحميل المبيعات المرتبطة بهذا الجهاز
                await LoadDeviceSalesAsync();

                // تحميل سجلات الصيانة
                await LoadDeviceMaintenanceAsync();

                // تحميل سجلات التنصيبات
                await LoadDeviceInstallationsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات المرتبطة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadDeviceSalesAsync()
        {
            try
            {
                _deviceSales = await App.DatabaseContext.Sales
                    .Where(s => s.DeviceName.Contains(_device.Name) ||
                               s.DeviceName.Contains(_device.SerialNumber))
                    .OrderByDescending(s => s.SaleDate)
                    .ToListAsync();

                SalesDataGrid.ItemsSource = _deviceSales;

                // تحديث الإحصائيات
                var totalSales = _deviceSales.Count;
                var totalRevenue = _deviceSales.Sum(s => s.TotalAmount);
                SalesCountTextBlock.Text = $"إجمالي المبيعات: {totalSales} | إجمالي الإيرادات: {totalRevenue:N0} د.ع";

                // تحديث الملخص السريع
                SalesCountSummary.Text = $"{totalSales} مبيعات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadDeviceMaintenanceAsync()
        {
            try
            {
                _deviceMaintenance = await App.DatabaseContext.MaintenanceRecords
                    .Where(m => (m.DeviceId == _device.Id || m.MedicalDeviceId == _device.Id) ||
                               m.DeviceName.Contains(_device.Name) ||
                               m.SerialNumber.Contains(_device.SerialNumber))
                    .OrderByDescending(m => m.MaintenanceDate)
                    .ToListAsync();

                MaintenanceDataGrid.ItemsSource = _deviceMaintenance;

                // تحديث الإحصائيات
                var totalMaintenance = _deviceMaintenance.Count;
                var totalCost = _deviceMaintenance.Sum(m => m.Cost);
                var lastMaintenance = _deviceMaintenance.FirstOrDefault()?.MaintenanceDate.ToString("dd/MM/yyyy") ?? "لا يوجد";
                MaintenanceCountTextBlock.Text = $"إجمالي الصيانات: {totalMaintenance} | إجمالي التكلفة: {totalCost:N0} د.ع | آخر صيانة: {lastMaintenance}";

                // تحديث الملخص السريع
                MaintenanceCountSummary.Text = $"{totalMaintenance} صيانات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل سجلات الصيانة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadDeviceInstallationsAsync()
        {
            try
            {
                _deviceInstallations = await App.DatabaseContext.DeviceInstallations
                    .Where(i => i.MedicalDeviceId == _device.Id)
                    .OrderByDescending(i => i.InstallationDate)
                    .ToListAsync();

                InstallationsDataGrid.ItemsSource = _deviceInstallations;

                // تحديث الإحصائيات
                var totalInstallations = _deviceInstallations.Count;
                var totalCost = _deviceInstallations.Sum(i => i.InstallationCost);
                var activeInstallations = _deviceInstallations.Count(i => i.InstallationStatus == "مكتمل");
                InstallationsCountTextBlock.Text = $"إجمالي التنصيبات: {totalInstallations} | النشطة: {activeInstallations} | إجمالي التكلفة: {totalCost:N0} د.ع";

                // تحديث الملخص السريع
                InstallationsCountSummary.Text = $"{totalInstallations} تنصيبات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل سجلات التنصيبات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ManageSerialNumbersBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageSerialWindow = new ManageSerialNumbersWindow(_device.Id);
            manageSerialWindow.ShowDialog();
            
            // تحديث الأرقام التسلسلية بعد الإغلاق
            _ = LoadSerialNumbersAsync();
        }
        
        private void EditDeviceBtn_Click(object sender, RoutedEventArgs e)
        {
            var editWindow = new AddEditDeviceWindow(_device);
            if (editWindow.ShowDialog() == true)
            {
                // إعادة تحميل البيانات بعد التعديل
                LoadDeviceDetailsAsync();
            }
        }
        
        private void PrintDetailsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreatePrintDocument();
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, $"تفاصيل الجهاز - {_device.Name}");
                    MessageBox.Show("تم طباعة التفاصيل بنجاح", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FlowDocument CreatePrintDocument()
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);
            document.FontFamily = new FontFamily("Arial");
            document.FontSize = 12;

            // العنوان الرئيسي
            var title = new Paragraph(new Run($"تقرير تفاصيل الجهاز الطبي - {_device.Name}"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            // تاريخ التقرير
            var dateInfo = new Paragraph(new Run($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}"))
            {
                FontSize = 10,
                TextAlignment = TextAlignment.Right,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(dateInfo);

            // المعلومات الأساسية
            AddSectionToDocument(document, "المعلومات الأساسية", new[]
            {
                $"اسم الجهاز: {_device.Name}",
                $"الماركة: {_device.Brand}",
                $"الموديل: {_device.Model}",
                $"الرقم التسلسلي: {_device.SerialNumber}",
                $"الفئة: {_device.Category}",
                $"الحالة: {_device.Status}",
                $"الوصف: {_device.Description}"
            });

            // المعلومات المالية
            AddSectionToDocument(document, "المعلومات المالية", new[]
            {
                $"سعر الشراء: {_device.PurchasePrice:N0} د.ع",
                $"سعر البيع: {_device.SellingPrice:N0} د.ع",
                $"المورد: {_device.Supplier}",
                $"الموقع: {_device.Location}"
            });

            // التواريخ المهمة
            AddSectionToDocument(document, "التواريخ المهمة", new[]
            {
                $"تاريخ الشراء: {_device.PurchaseDate:dd/MM/yyyy}",
                $"تاريخ الإنشاء: {_device.CreatedDate:dd/MM/yyyy}",
                $"بداية الضمان: {_device.WarrantyStartDate:dd/MM/yyyy}",
                $"نهاية الضمان: {_device.WarrantyEndDate:dd/MM/yyyy}"
            });

            // إحصائيات الأرقام التسلسلية
            if (_serialNumbers?.Any() == true)
            {
                var activeCount = _serialNumbers.Count(s => s.Status == "نشط");
                AddSectionToDocument(document, "الأرقام التسلسلية", new[]
                {
                    $"إجمالي المكونات: {_serialNumbers.Count}",
                    $"المكونات النشطة: {activeCount}",
                    $"المكونات المعطلة: {_serialNumbers.Count - activeCount}"
                });
            }

            // إحصائيات المبيعات
            if (_deviceSales?.Any() == true)
            {
                var totalRevenue = _deviceSales.Sum(s => s.TotalAmount);
                AddSectionToDocument(document, "إحصائيات المبيعات", new[]
                {
                    $"إجمالي المبيعات: {_deviceSales.Count}",
                    $"إجمالي الإيرادات: {totalRevenue:N0} د.ع",
                    $"متوسط سعر البيع: {(totalRevenue / _deviceSales.Count):N0} د.ع"
                });
            }

            // إحصائيات الصيانة
            if (_deviceMaintenance?.Any() == true)
            {
                var totalCost = _deviceMaintenance.Sum(m => m.Cost);
                var lastMaintenance = _deviceMaintenance.FirstOrDefault()?.MaintenanceDate.ToString("dd/MM/yyyy") ?? "لا يوجد";
                AddSectionToDocument(document, "إحصائيات الصيانة", new[]
                {
                    $"إجمالي الصيانات: {_deviceMaintenance.Count}",
                    $"إجمالي تكلفة الصيانة: {totalCost:N0} د.ع",
                    $"آخر صيانة: {lastMaintenance}"
                });
            }

            // إحصائيات التنصيبات
            if (_deviceInstallations?.Any() == true)
            {
                var totalCost = _deviceInstallations.Sum(i => i.InstallationCost);
                var activeInstallations = _deviceInstallations.Count(i => i.InstallationStatus == "مكتمل");
                AddSectionToDocument(document, "إحصائيات التنصيبات", new[]
                {
                    $"إجمالي التنصيبات: {_deviceInstallations.Count}",
                    $"التنصيبات النشطة: {activeInstallations}",
                    $"إجمالي تكلفة التنصيب: {totalCost:N0} د.ع"
                });
            }

            return document;
        }

        private void AddSectionToDocument(FlowDocument document, string sectionTitle, string[] items)
        {
            // عنوان القسم
            var sectionHeader = new Paragraph(new Run(sectionTitle))
            {
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 15, 0, 5),
                Foreground = System.Windows.Media.Brushes.DarkBlue
            };
            document.Blocks.Add(sectionHeader);

            // عناصر القسم
            foreach (var item in items)
            {
                if (!string.IsNullOrEmpty(item))
                {
                    var paragraph = new Paragraph(new Run($"• {item}"))
                    {
                        Margin = new Thickness(20, 2, 0, 2)
                    };
                    document.Blocks.Add(paragraph);
                }
            }
        }
        
        private void OpenUserManualBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.UserManualPath);
        }
        
        private void OpenMaintenanceManualBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.MaintenanceManualPath);
        }
        
        private void OpenOriginCertificateBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.OriginCertificatePath);
        }
        
        private void OpenOfficialCertificationsBtn_Click(object sender, RoutedEventArgs e)
        {
            OpenDocument(_device.OfficialCertificationsPath);
        }
        
        private void OpenDocument(string filePath)
        {
            try
            {
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود أو تم حذفه", "ملف غير موجود", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
