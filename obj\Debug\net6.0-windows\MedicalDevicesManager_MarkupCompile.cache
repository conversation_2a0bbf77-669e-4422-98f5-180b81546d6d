﻿MedicalDevicesManager


winexe
C#
.cs
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\obj\Debug\net6.0-windows\
MedicalDevicesManager
none
false
TRACE;DEBUG;NET;NET6_0;NETCOREAPP;WINDOWS;WINDOWS7_0;NET5_0_OR_GREATER;NET6_0_OR_GREATER;NETCOREAPP3_0_OR_GREATER;NETCOREAPP3_1_OR_GREATER;WINDOWS7_0_OR_GREATER
D:\MedicalBusiness\MedicalBrokerManager\WorkingSystem\MedicalDevicesManager_BACKUP_EXPORT_INVOICE_COMPLETE\App.xaml
************

42-**********
243111062339
MainWindow.xaml;Windows\AddEditCustomerWindow.xaml;Windows\AddEditDeviceWindow.xaml;Windows\AddEditInstallationWindow.xaml;Windows\AddEditInventoryWindow.xaml;Windows\AddEditMaintenanceWindow.xaml;Windows\AddEditSaleWindow.xaml;Windows\AddEditShipmentWindow.xaml;Windows\AddEditSupplierWindow.xaml;Windows\AdvancedInventorySettingsWindow.xaml;Windows\BackupWindow.xaml;Windows\CreateBackupWindow.xaml;Windows\DeviceDetailsWindow.xaml;Windows\ExportWindow.xaml;Windows\InventoryItemReportWindow.xaml;Windows\InventorySelectionWindow.xaml;Windows\ManageCategoriesWindow.xaml;Windows\ManageCitiesWindow.xaml;Windows\ManageCustomerTypesWindow.xaml;Windows\ManageInventoryCategoriesWindow.xaml;Windows\ManageRecipientsWindow.xaml;Windows\ManageSerialNumbersWindow.xaml;Windows\ManageTechniciansWindow.xaml;Windows\NotificationsWindow.xaml;Windows\ReportsWindow.xaml;Windows\SettingsWindow.xaml;Windows\SmartSystemWindow.xaml;

False

