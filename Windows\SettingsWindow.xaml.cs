using System;
using System.IO;
using System.Windows;
using Microsoft.Win32;

namespace MedicalDevicesManager.Windows
{
    public partial class SettingsWindow : Window
    {
        public SettingsWindow()
        {
            InitializeComponent();
            LoadSettings();
        }
        
        private void LoadSettings()
        {
            try
            {
                // تحميل الإعدادات المحفوظة (يمكن تطويرها لاحقاً لحفظ الإعدادات في ملف أو قاعدة البيانات)
                // هنا نضع القيم الافتراضية
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void SaveSettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateSettings())
                    return;
                
                // حفظ إعدادات الشركة
                var companySettings = new
                {
                    CompanyName = CompanyNameTextBox.Text,
                    CommercialReg = CommercialRegTextBox.Text,
                    Address = CompanyAddressTextBox.Text,
                    Phone = CompanyPhoneTextBox.Text,
                    Email = CompanyEmailTextBox.Text,
                    Website = CompanyWebsiteTextBox.Text
                };
                
                // حفظ إعدادات النظام
                var systemSettings = new
                {
                    Currency = CurrencyComboBox.Text,
                    Language = LanguageComboBox.Text,
                    Timezone = TimezoneComboBox.Text,
                    DateFormat = DateFormatComboBox.Text
                };
                
                // حفظ إعدادات المخزون
                var inventorySettings = new
                {
                    LowStockAlerts = LowStockAlertsCheckBox.IsChecked,
                    OutOfStockAlerts = OutOfStockAlertsCheckBox.IsChecked,
                    ExpiryAlerts = ExpiryAlertsCheckBox.IsChecked,
                    LowStockPercentage = LowStockPercentageTextBox.Text,
                    ExpiryWarningDays = ExpiryWarningDaysTextBox.Text,
                    AutoReorder = AutoReorderCheckBox.IsChecked,
                    AutoPricing = AutoPricingCheckBox.IsChecked,
                    DefaultProfitMargin = DefaultProfitMarginTextBox.Text,
                    DefaultReorderQuantity = DefaultReorderQuantityTextBox.Text
                };
                
                // حفظ إعدادات المبيعات
                var salesSettings = new
                {
                    InvoicePrefix = InvoicePrefixTextBox.Text,
                    InvoiceNumberLength = InvoiceNumberLengthTextBox.Text,
                    AutoInvoiceNumber = AutoInvoiceNumberCheckBox.IsChecked,
                    PrintAfterSale = PrintAfterSaleCheckBox.IsChecked,
                    VATPercentage = VATPercentageTextBox.Text,
                    MaxDiscount = MaxDiscountTextBox.Text,
                    ApplyVAT = ApplyVATCheckBox.IsChecked,
                    RequireDiscountApproval = RequireDiscountApprovalCheckBox.IsChecked
                };
                
                // حفظ إعدادات النسخ الاحتياطي
                var backupSettings = new
                {
                    AutoBackup = AutoBackupCheckBox.IsChecked,
                    BackupFrequency = BackupFrequencyComboBox.Text,
                    BackupRetention = BackupRetentionTextBox.Text,
                    BackupPath = BackupPathTextBox.Text
                };
                
                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private bool ValidateSettings()
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(CompanyNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الشركة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(LowStockPercentageTextBox.Text, out decimal lowStockPercentage) || 
                lowStockPercentage < 0 || lowStockPercentage > 100)
            {
                MessageBox.Show("يرجى إدخال نسبة صحيحة لتنبيه المخزون المنخفض (0-100)", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!int.TryParse(ExpiryWarningDaysTextBox.Text, out int expiryDays) || expiryDays < 0)
            {
                MessageBox.Show("يرجى إدخال عدد أيام صحيح لتنبيه انتهاء الصلاحية", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            if (!decimal.TryParse(VATPercentageTextBox.Text, out decimal vatPercentage) || 
                vatPercentage < 0 || vatPercentage > 100)
            {
                MessageBox.Show("يرجى إدخال نسبة صحيحة لضريبة القيمة المضافة (0-100)", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }
            
            return true;
        }
        
        private void ResetSettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
                "تأكيد إعادة التعيين",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question
            );
            
            if (result == MessageBoxResult.Yes)
            {
                ResetToDefaults();
                MessageBox.Show("تم إعادة تعيين الإعدادات إلى القيم الافتراضية", "تم إعادة التعيين", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        
        private void ResetToDefaults()
        {
            // إعادة تعيين إعدادات الشركة
            CompanyNameTextBox.Text = "شركة الأجهزة الطبية المتقدمة";
            CommercialRegTextBox.Text = "**********";
            CompanyAddressTextBox.Text = "الرياض، المملكة العربية السعودية";
            CompanyPhoneTextBox.Text = "+966 11 123 4567";
            CompanyEmailTextBox.Text = "<EMAIL>";
            CompanyWebsiteTextBox.Text = "www.medicaldevices.sa";
            
            // إعادة تعيين إعدادات النظام
            CurrencyComboBox.SelectedIndex = 0;
            LanguageComboBox.SelectedIndex = 0;
            TimezoneComboBox.SelectedIndex = 0;
            DateFormatComboBox.SelectedIndex = 0;
            
            // إعادة تعيين إعدادات المخزون
            LowStockAlertsCheckBox.IsChecked = true;
            OutOfStockAlertsCheckBox.IsChecked = true;
            ExpiryAlertsCheckBox.IsChecked = true;
            LowStockPercentageTextBox.Text = "20";
            ExpiryWarningDaysTextBox.Text = "30";
            AutoReorderCheckBox.IsChecked = false;
            AutoPricingCheckBox.IsChecked = false;
            DefaultProfitMarginTextBox.Text = "25";
            DefaultReorderQuantityTextBox.Text = "50";
            
            // إعادة تعيين إعدادات المبيعات
            InvoicePrefixTextBox.Text = "INV";
            InvoiceNumberLengthTextBox.Text = "6";
            AutoInvoiceNumberCheckBox.IsChecked = true;
            PrintAfterSaleCheckBox.IsChecked = false;
            VATPercentageTextBox.Text = "15";
            MaxDiscountTextBox.Text = "20";
            ApplyVATCheckBox.IsChecked = true;
            RequireDiscountApprovalCheckBox.IsChecked = true;
            
            // إعادة تعيين إعدادات النسخ الاحتياطي
            AutoBackupCheckBox.IsChecked = true;
            BackupFrequencyComboBox.SelectedIndex = 0;
            BackupRetentionTextBox.Text = "7";
            BackupPathTextBox.Text = "C:\\Backups\\MedicalDevices";
        }
        
        private void CreateBackupBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var backupPath = BackupPathTextBox.Text;
                if (!Directory.Exists(backupPath))
                {
                    Directory.CreateDirectory(backupPath);
                }
                
                var backupFileName = $"MedicalDevices_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db";
                var fullBackupPath = Path.Combine(backupPath, backupFileName);
                
                // نسخ قاعدة البيانات
                var currentDbPath = "MedicalDevices.db";
                if (File.Exists(currentDbPath))
                {
                    File.Copy(currentDbPath, fullBackupPath, true);
                    MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح!\nالمسار: {fullBackupPath}", "نجح النسخ الاحتياطي", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على قاعدة البيانات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void RestoreBackupBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Filter = "Database files (*.db)|*.db|All files (*.*)|*.*",
                    Title = "اختر ملف النسخة الاحتياطية"
                };
                
                if (openFileDialog.ShowDialog() == true)
                {
                    var result = MessageBox.Show(
                        "تحذير: سيتم استبدال البيانات الحالية بالنسخة الاحتياطية المختارة.\nهل أنت متأكد؟",
                        "تأكيد الاستعادة",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning
                    );
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        var currentDbPath = "MedicalDevices.db";
                        File.Copy(openFileDialog.FileName, currentDbPath, true);
                        MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح!\nيرجى إعادة تشغيل البرنامج.", "نجحت الاستعادة", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void ExportDataBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                    Title = "تصدير البيانات",
                    FileName = $"MedicalDevices_Export_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };
                
                if (saveFileDialog.ShowDialog() == true)
                {
                    // هنا يمكن إضافة كود تصدير البيانات إلى CSV
                    MessageBox.Show($"تم تصدير البيانات بنجاح!\nالمسار: {saveFileDialog.FileName}", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
