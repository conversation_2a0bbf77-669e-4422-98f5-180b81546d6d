using System;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;

namespace MedicalDevicesManager.Windows
{
    public partial class AddEditDeviceWindow : Window
    {
        private MedicalDevice _device;
        private bool _isEditMode;
        
        public AddEditDeviceWindow(MedicalDevice device = null)
        {
            InitializeComponent();
            _device = device;
            _isEditMode = device != null;
            
            LoadSuppliersAsync();
            LoadCategoriesAsync();
            
            if (_isEditMode)
            {
                TitleBlock.Text = "تعديل جهاز طبي";
                LoadDeviceData();
            }
            else
            {
                // تعيين القيم الافتراضية
                PurchaseDatePicker.SelectedDate = DateTime.Now;
                WarrantyStartDatePicker.SelectedDate = DateTime.Now;
                WarrantyEndDatePicker.SelectedDate = DateTime.Now.AddYears(1);
                LocationComboBox.SelectedIndex = 0;
                StatusComboBox.SelectedIndex = 0;
            }
        }
        
        private async void LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await App.DatabaseContext.Suppliers
                    .Where(s => s.Status == "نشط")
                    .Select(s => s.Name)
                    .ToListAsync();
                
                foreach (var supplier in suppliers)
                {
                    SupplierComboBox.Items.Add(supplier);
                }
                
                if (SupplierComboBox.Items.Count > 0)
                    SupplierComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadCategoriesAsync()
        {
            try
            {
                var categories = await App.DatabaseContext.DeviceCategories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                CategoryComboBox.Items.Clear();

                foreach (var category in categories)
                {
                    CategoryComboBox.Items.Add($"{category.Icon} {category.Name}");
                }

                // إضافة فئات افتراضية إذا لم توجد فئات
                if (!categories.Any())
                {
                    CategoryComboBox.Items.Add("🏥 أجهزة الأشعة");
                    CategoryComboBox.Items.Add("🔬 أجهزة المختبر");
                    CategoryComboBox.Items.Add("💉 أجهزة الحقن");
                    CategoryComboBox.Items.Add("🩺 أجهزة الفحص");
                    CategoryComboBox.Items.Add("❤️ أجهزة القلب");
                    CategoryComboBox.Items.Add("🧠 أجهزة الأعصاب");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDeviceData()
        {
            if (_device == null) return;
            
            NameTextBox.Text = _device.Name;
            BrandTextBox.Text = _device.Brand;
            ModelTextBox.Text = _device.Model;
            SerialNumberTextBox.Text = _device.SerialNumber;
            DescriptionTextBox.Text = _device.Description;
            PurchasePriceTextBox.Text = _device.PurchasePrice.ToString();
            SellingPriceTextBox.Text = _device.SellingPrice.ToString();
            PurchaseDatePicker.SelectedDate = _device.PurchaseDate;
            WarrantyStartDatePicker.SelectedDate = _device.WarrantyStartDate;
            WarrantyEndDatePicker.SelectedDate = _device.WarrantyEndDate;
            
            // تعيين الفئة
            foreach (var item in CategoryComboBox.Items)
            {
                if (item.ToString().Contains(_device.Category))
                {
                    CategoryComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين المورد
            foreach (var item in SupplierComboBox.Items)
            {
                if (item.ToString() == _device.Supplier)
                {
                    SupplierComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الموقع
            foreach (var item in LocationComboBox.Items)
            {
                if (item.ToString().Contains(_device.Location))
                {
                    LocationComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الحالة
            foreach (var item in StatusComboBox.Items)
            {
                if (item.ToString().Contains(_device.Status))
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }

            // تحميل المستندات
            UserManualPathTextBox.Text = _device.UserManualPath ?? "";
            MaintenanceManualPathTextBox.Text = _device.MaintenanceManualPath ?? "";
            OriginCertificatePathTextBox.Text = _device.OriginCertificatePath ?? "";
            OfficialCertificationsPathTextBox.Text = _device.OfficialCertificationsPath ?? "";

            // تفعيل زر إدارة الأرقام التسلسلية للأجهزة الموجودة
            ManageSerialNumbersBtn.IsEnabled = true;
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;
            
            try
            {
                if (_isEditMode)
                {
                    UpdateDeviceData();
                    App.DatabaseContext.MedicalDevices.Update(_device);
                }
                else
                {
                    _device = new MedicalDevice();
                    UpdateDeviceData();
                    _device.CreatedDate = DateTime.Now;
                    App.DatabaseContext.MedicalDevices.Add(_device);
                    
                    // إضافة الجهاز إلى المخزون تلقائياً
                    var inventoryItem = new InventoryItem
                    {
                        Name = _device.Name,
                        Category = _device.Category,
                        Description = _device.Description,
                        CurrentStock = 1,
                        MinimumStock = 1,
                        Unit = "جهاز",
                        UnitPrice = _device.PurchasePrice,
                        Location = _device.Location,
                        Status = "متاح",
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };
                    
                    App.DatabaseContext.InventoryItems.Add(inventoryItem);
                }
                
                _device.LastUpdated = DateTime.Now;

                try
                {
                    await App.DatabaseContext.SaveChangesAsync();

                    // تفعيل زر إدارة الأرقام التسلسلية بعد الحفظ
                    ManageSerialNumbersBtn.IsEnabled = true;

                    MessageBox.Show(
                    _isEditMode ? "تم تحديث الجهاز بنجاح!" : "تم إضافة الجهاز بنجاح!",
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );

                    DialogResult = true;
                    Close();
                }
                catch (Exception saveEx)
                {
                    MessageBox.Show($"خطأ في حفظ البيانات: {saveEx.Message}\n\nتفاصيل: {saveEx.InnerException?.Message}",
                        "خطأ في الحفظ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة البيانات: {ex.Message}\n\nتفاصيل: {ex.InnerException?.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void UpdateDeviceData()
        {
            _device.Name = NameTextBox.Text.Trim();
            _device.Brand = BrandTextBox.Text.Trim();
            _device.Model = ModelTextBox.Text.Trim();
            _device.SerialNumber = SerialNumberTextBox.Text.Trim();
            _device.Category = CategoryComboBox.Text;
            _device.Description = DescriptionTextBox.Text.Trim();
            _device.PurchasePrice = decimal.Parse(PurchasePriceTextBox.Text);
            _device.SellingPrice = decimal.Parse(SellingPriceTextBox.Text);
            _device.PurchaseDate = PurchaseDatePicker.SelectedDate ?? DateTime.Now;
            _device.WarrantyStartDate = WarrantyStartDatePicker.SelectedDate ?? DateTime.Now;
            _device.WarrantyEndDate = WarrantyEndDatePicker.SelectedDate ?? DateTime.Now.AddYears(1);
            _device.Supplier = SupplierComboBox.Text;
            _device.Location = LocationComboBox.Text;
            _device.Status = StatusComboBox.Text;

            // حفظ مسارات المستندات
            _device.UserManualPath = UserManualPathTextBox.Text.Trim();
            _device.MaintenanceManualPath = MaintenanceManualPathTextBox.Text.Trim();
            _device.OriginCertificatePath = OriginCertificatePathTextBox.Text.Trim();
            _device.OfficialCertificationsPath = OfficialCertificationsPathTextBox.Text.Trim();
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الجهاز", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(BrandTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الماركة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                BrandTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(ModelTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الموديل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ModelTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(SerialNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الرقم التسلسلي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SerialNumberTextBox.Focus();
                return false;
            }
            
            if (CategoryComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الفئة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryComboBox.Focus();
                return false;
            }
            
            if (!decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) || purchasePrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر شراء صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PurchasePriceTextBox.Focus();
                return false;
            }
            
            if (!decimal.TryParse(SellingPriceTextBox.Text, out decimal sellingPrice) || sellingPrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر بيع صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SellingPriceTextBox.Focus();
                return false;
            }
            
            if (PurchaseDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ الشراء", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PurchaseDatePicker.Focus();
                return false;
            }
            
            if (WarrantyStartDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ بداية الضمان", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                WarrantyStartDatePicker.Focus();
                return false;
            }
            
            if (WarrantyEndDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ نهاية الضمان", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                WarrantyEndDatePicker.Focus();
                return false;
            }
            
            if (SupplierComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المورد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SupplierComboBox.Focus();
                return false;
            }
            
            if (LocationComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموقع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                LocationComboBox.Focus();
                return false;
            }
            
            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الحالة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                StatusComboBox.Focus();
                return false;
            }
            
            return true;
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // دوال إدارة الفئات والملفات
        private void ManageCategoriesBtn_Click(object sender, RoutedEventArgs e)
        {
            var manageCategoriesWindow = new ManageCategoriesWindow();
            if (manageCategoriesWindow.ShowDialog() == true)
            {
                LoadCategoriesAsync();
            }
        }

        // دوال تصفح الملفات
        private void BrowseUserManualBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(UserManualPathTextBox, "اختيار كتيب المستخدم");
        }

        private void BrowseMaintenanceManualBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(MaintenanceManualPathTextBox, "اختيار كتيب الصيانة");
        }

        private void BrowseOriginCertificateBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(OriginCertificatePathTextBox, "اختيار شهادة المنشأ");
        }

        private void BrowseOfficialCertificationsBtn_Click(object sender, RoutedEventArgs e)
        {
            BrowseFile(OfficialCertificationsPathTextBox, "اختيار المصادقات الرسمية");
        }

        // دوال مسح الملفات
        private void ClearUserManualBtn_Click(object sender, RoutedEventArgs e)
        {
            UserManualPathTextBox.Text = "";
        }

        private void ClearMaintenanceManualBtn_Click(object sender, RoutedEventArgs e)
        {
            MaintenanceManualPathTextBox.Text = "";
        }

        private void ClearOriginCertificateBtn_Click(object sender, RoutedEventArgs e)
        {
            OriginCertificatePathTextBox.Text = "";
        }

        private void ClearOfficialCertificationsBtn_Click(object sender, RoutedEventArgs e)
        {
            OfficialCertificationsPathTextBox.Text = "";
        }

        // دالة مساعدة لتصفح الملفات
        private void BrowseFile(TextBox textBox, string title)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = title,
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات Word (*.doc;*.docx)|*.doc;*.docx|ملفات الصور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                textBox.Text = openFileDialog.FileName;
            }
        }

        private void ManageSerialNumbersBtn_Click(object sender, RoutedEventArgs e)
        {
            if (_device != null && _device.Id > 0)
            {
                var manageSerialWindow = new ManageSerialNumbersWindow(_device.Id);
                manageSerialWindow.ShowDialog();
            }
            else
            {
                MessageBox.Show("يجب حفظ الجهاز أولاً قبل إدارة الأرقام التسلسلية", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}
