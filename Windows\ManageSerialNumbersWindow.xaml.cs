using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;

namespace MedicalDevicesManager.Windows
{
    public partial class ManageSerialNumbersWindow : Window
    {
        private int _deviceId;
        private MedicalDevice _device;
        private List<DeviceSerialNumber> _serialNumbers;
        private DeviceSerialNumber _editingSerialNumber = null;
        
        public ManageSerialNumbersWindow(int deviceId)
        {
            InitializeComponent();
            _deviceId = deviceId;
            LoadDeviceInfoAsync();
            LoadSerialNumbersAsync();
        }
        
        private async void LoadDeviceInfoAsync()
        {
            try
            {
                _device = await App.DatabaseContext.MedicalDevices
                    .FirstOrDefaultAsync(d => d.Id == _deviceId);
                
                if (_device != null)
                {
                    DeviceNameTextBlock.Text = _device.Name;
                    DeviceModelTextBlock.Text = _device.Model;
                    Title = $"إدارة الأرقام التسلسلية - {_device.Name}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات الجهاز: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void LoadSerialNumbersAsync()
        {
            try
            {
                _serialNumbers = await App.DatabaseContext.DeviceSerialNumbers
                    .Where(s => s.DeviceId == _deviceId && s.IsActive)
                    .OrderBy(s => s.ComponentName)
                    .ToListAsync();
                
                SerialNumbersDataGrid.ItemsSource = _serialNumbers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأرقام التسلسلية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void AddSerialBtn_Click(object sender, RoutedEventArgs e)
        {
            _editingSerialNumber = null;
            ClearForm();
            EditPanelTitle.Text = "📝 إضافة رقم تسلسلي جديد";
            EditPanel.Visibility = Visibility.Visible;
            SerialNumberTextBox.Focus();
        }
        
        private void EditSerialBtn_Click(object sender, RoutedEventArgs e)
        {
            if (SerialNumbersDataGrid.SelectedItem is DeviceSerialNumber selectedSerial)
            {
                _editingSerialNumber = selectedSerial;
                LoadSerialToForm(selectedSerial);
                EditPanelTitle.Text = "✏️ تعديل الرقم التسلسلي";
                EditPanel.Visibility = Visibility.Visible;
                SerialNumberTextBox.Focus();
            }
            else
            {
                MessageBox.Show("يرجى اختيار رقم تسلسلي للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private async void DeleteSerialBtn_Click(object sender, RoutedEventArgs e)
        {
            if (SerialNumbersDataGrid.SelectedItem is DeviceSerialNumber selectedSerial)
            {
                var result = MessageBox.Show(
                    $"هل تريد حذف الرقم التسلسلي '{selectedSerial.SerialNumber}' للمكون '{selectedSerial.ComponentName}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        selectedSerial.IsActive = false;
                        selectedSerial.LastUpdated = DateTime.Now;
                        await App.DatabaseContext.SaveChangesAsync();
                        
                        MessageBox.Show("تم حذف الرقم التسلسلي بنجاح!", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadSerialNumbersAsync();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الرقم التسلسلي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار رقم تسلسلي للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private async void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;
                
            try
            {
                if (_editingSerialNumber == null)
                {
                    // إضافة رقم تسلسلي جديد
                    var newSerial = new DeviceSerialNumber
                    {
                        DeviceId = _deviceId,
                        SerialNumber = SerialNumberTextBox.Text.Trim(),
                        ComponentName = ComponentNameTextBox.Text.Trim(),
                        ComponentType = (ComponentTypeComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "رئيسي",
                        Description = DescriptionTextBox.Text.Trim(),
                        Location = LocationTextBox.Text.Trim(),
                        Status = (StatusComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "نشط",
                        InstallationDate = InstallationDatePicker.SelectedDate,
                        Notes = NotesTextBox.Text.Trim(),
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        LastUpdated = DateTime.Now
                    };
                    
                    App.DatabaseContext.DeviceSerialNumbers.Add(newSerial);
                    await App.DatabaseContext.SaveChangesAsync();
                    
                    MessageBox.Show("تم إضافة الرقم التسلسلي بنجاح!", "نجحت الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // تعديل رقم تسلسلي موجود
                    _editingSerialNumber.SerialNumber = SerialNumberTextBox.Text.Trim();
                    _editingSerialNumber.ComponentName = ComponentNameTextBox.Text.Trim();
                    _editingSerialNumber.ComponentType = (ComponentTypeComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "رئيسي";
                    _editingSerialNumber.Description = DescriptionTextBox.Text.Trim();
                    _editingSerialNumber.Location = LocationTextBox.Text.Trim();
                    _editingSerialNumber.Status = (StatusComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "نشط";
                    _editingSerialNumber.InstallationDate = InstallationDatePicker.SelectedDate;
                    _editingSerialNumber.Notes = NotesTextBox.Text.Trim();
                    _editingSerialNumber.LastUpdated = DateTime.Now;
                    
                    await App.DatabaseContext.SaveChangesAsync();
                    
                    MessageBox.Show("تم تعديل الرقم التسلسلي بنجاح!", "نجح التعديل", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                
                LoadSerialNumbersAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الرقم التسلسلي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }
        
        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
        
        private void SerialNumbersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            EditSerialBtn.IsEnabled = SerialNumbersDataGrid.SelectedItem != null;
            DeleteSerialBtn.IsEnabled = SerialNumbersDataGrid.SelectedItem != null;
        }
        
        private void LoadSerialToForm(DeviceSerialNumber serial)
        {
            SerialNumberTextBox.Text = serial.SerialNumber;
            ComponentNameTextBox.Text = serial.ComponentName;
            DescriptionTextBox.Text = serial.Description;
            LocationTextBox.Text = serial.Location;
            NotesTextBox.Text = serial.Notes;
            InstallationDatePicker.SelectedDate = serial.InstallationDate;
            
            // تعيين نوع المكون
            foreach (ComboBoxItem item in ComponentTypeComboBox.Items)
            {
                if (item.Content.ToString() == serial.ComponentType)
                {
                    ComponentTypeComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تعيين الحالة
            foreach (ComboBoxItem item in StatusComboBox.Items)
            {
                if (item.Content.ToString() == serial.Status)
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }
        }
        
        private void ClearForm()
        {
            _editingSerialNumber = null;
            EditPanel.Visibility = Visibility.Collapsed;
            
            SerialNumberTextBox.Text = "";
            ComponentNameTextBox.Text = "";
            DescriptionTextBox.Text = "";
            LocationTextBox.Text = "";
            NotesTextBox.Text = "";
            InstallationDatePicker.SelectedDate = null;
            ComponentTypeComboBox.SelectedIndex = 0;
            StatusComboBox.SelectedIndex = 0;
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(SerialNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الرقم التسلسلي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SerialNumberTextBox.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(ComponentNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المكون", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ComponentNameTextBox.Focus();
                return false;
            }
            
            // التحقق من عدم تكرار الرقم التسلسلي
            var existingSerial = _serialNumbers.FirstOrDefault(s => 
                s.SerialNumber.Equals(SerialNumberTextBox.Text.Trim(), StringComparison.OrdinalIgnoreCase) &&
                (_editingSerialNumber == null || s.Id != _editingSerialNumber.Id));
                
            if (existingSerial != null)
            {
                MessageBox.Show("هذا الرقم التسلسلي موجود بالفعل لهذا الجهاز", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SerialNumberTextBox.Focus();
                return false;
            }
            
            return true;
        }
    }
}
