<Window x:Class="MedicalDevicesManager.Windows.DeviceDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل الجهاز الطبي" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                CornerRadius="5" Padding="20" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="📋 تفاصيل الجهاز الطبي الشاملة" 
                           FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" 
                           Margin="0,0,0,10" Foreground="#1976D2"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="اسم الجهاز:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Grid.Column="1" x:Name="DeviceNameTextBlock" Text="" FontWeight="SemiBold" 
                               Foreground="#1976D2" VerticalAlignment="Center" FontSize="16" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Column="2" Text="الرقم التسلسلي:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Grid.Column="3" x:Name="SerialNumberTextBlock" Text="" FontWeight="SemiBold" 
                               Foreground="#1976D2" VerticalAlignment="Center" FontSize="16"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- ملخص سريع -->
        <Border Grid.Row="1" Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="SalesCountSummary" Text="0 مبيعات" FontWeight="SemiBold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock Text="المبيعات" FontSize="12" Foreground="#6C757D" HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="🔧" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="MaintenanceCountSummary" Text="0 صيانات" FontWeight="SemiBold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock Text="الصيانة" FontSize="12" Foreground="#6C757D" HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="⚙️" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="InstallationsCountSummary" Text="0 تنصيبات" FontWeight="SemiBold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock Text="التنصيبات" FontSize="12" Foreground="#6C757D" HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="📁" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="FilesCountSummary" Text="0 ملفات" FontWeight="SemiBold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock Text="الملفات" FontSize="12" Foreground="#6C757D" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- محتوى التفاصيل -->
        <TabControl Grid.Row="2" FontSize="14">
            
            <!-- تبويب المعلومات الأساسية -->
            <TabItem Header="📝 المعلومات الأساسية">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- المعلومات الأساسية -->
                        <GroupBox Grid.Row="0" Header="المعلومات الأساسية" Margin="0,0,0,20" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="الماركة:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="BrandTextBlock" Text="" Margin="0,0,20,10"/>
                                
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="الموديل:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="0" Grid.Column="3" x:Name="ModelTextBlock" Text="" Margin="0,0,0,10"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="الفئة:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="CategoryTextBlock" Text="" Margin="0,0,20,10"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="الحالة:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="1" Grid.Column="3" x:Name="StatusTextBlock" Text="" Margin="0,0,0,10"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="الوصف:" FontWeight="Bold" Margin="0,0,15,10" VerticalAlignment="Top"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" x:Name="DescriptionTextBlock" 
                                           Text="" TextWrapping="Wrap" Margin="0,0,0,10"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- المعلومات المالية -->
                        <GroupBox Grid.Row="1" Header="المعلومات المالية" Margin="0,0,0,20" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="سعر الشراء:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="PurchasePriceTextBlock" Text="" Margin="0,0,20,10"/>
                                
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="سعر البيع:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="0" Grid.Column="3" x:Name="SellingPriceTextBlock" Text="" Margin="0,0,0,10"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="المورد:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="SupplierTextBlock" Text="" Margin="0,0,20,10"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="الموقع:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="1" Grid.Column="3" x:Name="LocationTextBlock" Text="" Margin="0,0,0,10"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- معلومات التواريخ -->
                        <GroupBox Grid.Row="2" Header="التواريخ المهمة" Margin="0,0,0,20" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="تاريخ الشراء:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="PurchaseDateTextBlock" Text="" Margin="0,0,20,10"/>
                                
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="تاريخ الإنشاء:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="0" Grid.Column="3" x:Name="CreatedDateTextBlock" Text="" Margin="0,0,0,10"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="بداية الضمان:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="WarrantyStartTextBlock" Text="" Margin="0,0,20,10"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="نهاية الضمان:" FontWeight="Bold" Margin="0,0,15,10"/>
                                <TextBlock Grid.Row="1" Grid.Column="3" x:Name="WarrantyEndTextBlock" Text="" Margin="0,0,0,10"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- معلومات إضافية -->
                        <GroupBox Grid.Row="3" Header="معلومات إضافية" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="الملاحظات:" FontWeight="Bold" Margin="0,0,15,10" VerticalAlignment="Top"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="NotesTextBlock" Text="" TextWrapping="Wrap" Margin="0,0,0,10"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="المواصفات:" FontWeight="Bold" Margin="0,0,15,10" VerticalAlignment="Top"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="SpecificationsTextBlock" Text="" TextWrapping="Wrap"/>
                            </Grid>
                        </GroupBox>
                    </Grid>
                </ScrollViewer>
            </TabItem>
            
            <!-- تبويب الأرقام التسلسلية -->
            <TabItem Header="🔢 الأرقام التسلسلية">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="📋 قائمة الأرقام التسلسلية والمكونات" FontSize="16" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="0,0,20,0"/>
                        <Button x:Name="ManageSerialNumbersBtn" Content="🔧 إدارة الأرقام التسلسلية" 
                                Width="180" Height="35" Background="#17A2B8" Foreground="White" 
                                BorderThickness="0" FontSize="12" FontWeight="SemiBold" 
                                Click="ManageSerialNumbersBtn_Click"/>
                    </StackPanel>
                    
                    <DataGrid Grid.Row="1" x:Name="SerialNumbersDataGrid" 
                              AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA" FontSize="12">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الرقم التسلسلي" Binding="{Binding SerialNumber}" Width="150"/>
                            <DataGridTextColumn Header="اسم المكون" Binding="{Binding ComponentName}" Width="150"/>
                            <DataGridTextColumn Header="نوع المكون" Binding="{Binding ComponentType}" Width="120"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                            <DataGridTextColumn Header="الموقع" Binding="{Binding Location}" Width="120"/>
                            
                            <DataGridTemplateColumn Header="الحالة" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Status}" HorizontalAlignment="Center" FontWeight="Bold">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="نشط">
                                                            <Setter Property="Foreground" Value="#28A745"/>
                                                            <Setter Property="Text" Value="✅ نشط"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="معطل">
                                                            <Setter Property="Foreground" Value="#DC3545"/>
                                                            <Setter Property="Text" Value="❌ معطل"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="مفقود">
                                                            <Setter Property="Foreground" Value="#FD7E14"/>
                                                            <Setter Property="Text" Value="⚠️ مفقود"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="تحت الصيانة">
                                                            <Setter Property="Foreground" Value="#17A2B8"/>
                                                            <Setter Property="Text" Value="🔧 تحت الصيانة"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <DataGridTextColumn Header="تاريخ التركيب" Binding="{Binding InstallationDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                            <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>
                    
                    <TextBlock Grid.Row="2" x:Name="SerialNumbersCountTextBlock" Text="" 
                               FontWeight="SemiBold" Margin="0,10,0,0" Foreground="#6C757D"/>
                </Grid>
            </TabItem>
            
            <!-- تبويب المستندات -->
            <TabItem Header="📄 المستندات">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="📁 المستندات والملفات المرفقة" FontSize="16" FontWeight="Bold" 
                               Margin="0,0,0,15"/>
                    
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- كتيب المستخدم -->
                        <GroupBox Grid.Row="0" Header="📖 كتيب المستخدم" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" x:Name="UserManualTextBlock" Text="لا يوجد ملف مرفق" 
                                           VerticalAlignment="Center" Foreground="#6C757D"/>
                                <Button Grid.Column="1" x:Name="OpenUserManualBtn" Content="📂 فتح الملف" 
                                        Width="100" Height="30" Background="#17A2B8" Foreground="White" 
                                        BorderThickness="0" FontSize="12" Click="OpenUserManualBtn_Click" 
                                        IsEnabled="False"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- كتيب الصيانة -->
                        <GroupBox Grid.Row="1" Header="🔧 كتيب الصيانة" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" x:Name="MaintenanceManualTextBlock" Text="لا يوجد ملف مرفق" 
                                           VerticalAlignment="Center" Foreground="#6C757D"/>
                                <Button Grid.Column="1" x:Name="OpenMaintenanceManualBtn" Content="📂 فتح الملف" 
                                        Width="100" Height="30" Background="#17A2B8" Foreground="White" 
                                        BorderThickness="0" FontSize="12" Click="OpenMaintenanceManualBtn_Click" 
                                        IsEnabled="False"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- شهادة المنشأ -->
                        <GroupBox Grid.Row="2" Header="📜 شهادة المنشأ" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" x:Name="OriginCertificateTextBlock" Text="لا يوجد ملف مرفق" 
                                           VerticalAlignment="Center" Foreground="#6C757D"/>
                                <Button Grid.Column="1" x:Name="OpenOriginCertificateBtn" Content="📂 فتح الملف" 
                                        Width="100" Height="30" Background="#17A2B8" Foreground="White" 
                                        BorderThickness="0" FontSize="12" Click="OpenOriginCertificateBtn_Click" 
                                        IsEnabled="False"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- المصادقات الرسمية -->
                        <GroupBox Grid.Row="3" Header="🏛️ المصادقات الرسمية" Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" x:Name="OfficialCertificationsTextBlock" Text="لا يوجد ملف مرفق" 
                                           VerticalAlignment="Center" Foreground="#6C757D"/>
                                <Button Grid.Column="1" x:Name="OpenOfficialCertificationsBtn" Content="📂 فتح الملف" 
                                        Width="100" Height="30" Background="#17A2B8" Foreground="White" 
                                        BorderThickness="0" FontSize="12" Click="OpenOfficialCertificationsBtn_Click" 
                                        IsEnabled="False"/>
                            </Grid>
                        </GroupBox>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- تبويب المبيعات -->
            <TabItem Header="💰 المبيعات">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="📊 سجل مبيعات هذا الجهاز" FontSize="16" FontWeight="Bold"
                               Margin="0,0,0,15"/>

                    <DataGrid Grid.Row="1" x:Name="SalesDataGrid"
                              AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA" FontSize="12">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                            <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="150"/>
                            <DataGridTextColumn Header="تاريخ البيع" Binding="{Binding SaleDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="سعر الوحدة" Binding="{Binding UnitPrice, StringFormat=N0}" Width="120"/>
                            <DataGridTextColumn Header="الإجمالي" Binding="{Binding TotalAmount, StringFormat=N0}" Width="120"/>
                            <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="120"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <TextBlock Grid.Row="2" x:Name="SalesCountTextBlock" Text=""
                               FontWeight="SemiBold" Margin="0,10,0,0" Foreground="#6C757D"/>
                </Grid>
            </TabItem>

            <!-- تبويب الصيانة -->
            <TabItem Header="🔧 الصيانة">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="🛠️ سجل صيانة هذا الجهاز" FontSize="16" FontWeight="Bold"
                               Margin="0,0,0,15"/>

                    <DataGrid Grid.Row="1" x:Name="MaintenanceDataGrid"
                              AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA" FontSize="12">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="تاريخ الصيانة" Binding="{Binding MaintenanceDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                            <DataGridTextColumn Header="نوع الصيانة" Binding="{Binding MaintenanceType}" Width="120"/>
                            <DataGridTextColumn Header="الفني" Binding="{Binding TechnicianName}" Width="120"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                            <DataGridTextColumn Header="التكلفة" Binding="{Binding Cost, StringFormat=N0}" Width="100"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                            <DataGridTextColumn Header="التاريخ التالي" Binding="{Binding NextMaintenanceDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <TextBlock Grid.Row="2" x:Name="MaintenanceCountTextBlock" Text=""
                               FontWeight="SemiBold" Margin="0,10,0,0" Foreground="#6C757D"/>
                </Grid>
            </TabItem>

            <!-- تبويب التنصيبات -->
            <TabItem Header="⚙️ التنصيبات">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="🏗️ سجل تنصيبات هذا الجهاز" FontSize="16" FontWeight="Bold"
                               Margin="0,0,0,15"/>

                    <DataGrid Grid.Row="1" x:Name="InstallationsDataGrid"
                              AutoGenerateColumns="False" CanUserAddRows="False" IsReadOnly="True"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA" FontSize="12">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="تاريخ التنصيب" Binding="{Binding InstallationDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                            <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="150"/>
                            <DataGridTextColumn Header="الموقع" Binding="{Binding InstallationLocation}" Width="150"/>
                            <DataGridTextColumn Header="الفني" Binding="{Binding TechnicianName}" Width="120"/>
                            <DataGridTextColumn Header="التكلفة" Binding="{Binding InstallationCost, StringFormat=N0}" Width="100"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding InstallationStatus}" Width="100"/>
                            <DataGridTextColumn Header="سنوات الضمان" Binding="{Binding WarrantyYears}" Width="100"/>
                            <DataGridTextColumn Header="انتهاء الضمان" Binding="{Binding WarrantyEndDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <TextBlock Grid.Row="2" x:Name="InstallationsCountTextBlock" Text=""
                               FontWeight="SemiBold" Margin="0,10,0,0" Foreground="#6C757D"/>
                </Grid>
            </TabItem>

            <!-- تبويب الملفات والمرفقات -->
            <TabItem Header="📎 الملفات والمرفقات">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="📁 جميع الملفات والمستندات المرتبطة بالجهاز" FontSize="16" FontWeight="Bold"
                               Margin="0,0,0,15"/>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>

                            <!-- قسم كتيبات المستخدم والصيانة -->
                            <GroupBox Header="📖 كتيبات التشغيل والصيانة" Padding="15" Margin="0,0,0,15">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- كتيب المستخدم -->
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="كتيب المستخدم:" VerticalAlignment="Center" FontWeight="SemiBold"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="UserManualFileTextBlock" Text="لم يتم رفع ملف"
                                               Foreground="#6C757D" VerticalAlignment="Center" Margin="10,0"/>
                                    <Button Grid.Row="0" Grid.Column="2" x:Name="OpenUserManualFileBtn" Content="📖 فتح"
                                            Width="80" Height="30" IsEnabled="False" Click="OpenUserManualBtn_Click"
                                            Background="#17A2B8" Foreground="White" BorderThickness="0"/>

                                    <!-- كتيب الصيانة -->
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="كتيب الصيانة:" VerticalAlignment="Center" FontWeight="SemiBold" Margin="0,10,0,0"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="MaintenanceManualFileTextBlock" Text="لم يتم رفع ملف"
                                               Foreground="#6C757D" VerticalAlignment="Center" Margin="10,10,0,0"/>
                                    <Button Grid.Row="1" Grid.Column="2" x:Name="OpenMaintenanceManualFileBtn" Content="🔧 فتح"
                                            Width="80" Height="30" IsEnabled="False" Click="OpenMaintenanceManualBtn_Click"
                                            Background="#28A745" Foreground="White" BorderThickness="0" Margin="0,10,0,0"/>

                                    <!-- شهادة المنشأ -->
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="شهادة المنشأ:" VerticalAlignment="Center" FontWeight="SemiBold" Margin="0,10,0,0"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="OriginCertificateFileTextBlock" Text="لم يتم رفع ملف"
                                               Foreground="#6C757D" VerticalAlignment="Center" Margin="10,10,0,0"/>
                                    <Button Grid.Row="2" Grid.Column="2" x:Name="OpenOriginCertificateFileBtn" Content="📜 فتح"
                                            Width="80" Height="30" IsEnabled="False" Click="OpenOriginCertificateBtn_Click"
                                            Background="#FFC107" Foreground="White" BorderThickness="0" Margin="0,10,0,0"/>

                                    <!-- المصادقات الرسمية -->
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="المصادقات الرسمية:" VerticalAlignment="Center" FontWeight="SemiBold" Margin="0,10,0,0"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" x:Name="OfficialCertificationsFileTextBlock" Text="لم يتم رفع ملف"
                                               Foreground="#6C757D" VerticalAlignment="Center" Margin="10,10,0,0"/>
                                    <Button Grid.Row="3" Grid.Column="2" x:Name="OpenOfficialCertificationsFileBtn" Content="🏛️ فتح"
                                            Width="80" Height="30" IsEnabled="False" Click="OpenOfficialCertificationsBtn_Click"
                                            Background="#6F42C1" Foreground="White" BorderThickness="0" Margin="0,10,0,0"/>
                                </Grid>
                            </GroupBox>

                            <!-- قسم كتيبات التقارير -->
                            <GroupBox Header="📋 كتيبات التقارير" Padding="15" Margin="0,0,0,15">
                                <StackPanel x:Name="ReportBookletsPanel">
                                    <TextBlock Text="سيتم عرض كتيبات تقارير الصيانة والتنصيب هنا..."
                                               Foreground="#6C757D" FontStyle="Italic"/>
                                </StackPanel>
                            </GroupBox>

                            <!-- قسم الصور والمرفقات الإضافية -->
                            <GroupBox Header="🖼️ الصور والمرفقات الإضافية" Padding="15" Margin="0,0,0,15">
                                <StackPanel x:Name="AdditionalFilesPanel">
                                    <TextBlock Text="سيتم عرض الصور والمرفقات الإضافية هنا..."
                                               Foreground="#6C757D" FontStyle="Italic"/>
                                </StackPanel>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>

                    <TextBlock Grid.Row="2" x:Name="FilesCountTextBlock" Text=""
                               FontWeight="SemiBold" Margin="0,10,0,0" Foreground="#6C757D"/>
                </Grid>
            </TabItem>

        </TabControl>
        
        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="3" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" x:Name="EditDeviceBtn" Content="✏️ تعديل الجهاز" Width="130" Height="40" 
                    Background="#FFC107" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,10,0" Click="EditDeviceBtn_Click"/>
            
            <Button Grid.Column="1" x:Name="PrintDetailsBtn" Content="🖨️ طباعة التفاصيل" Width="140" Height="40" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Margin="0,0,20,0" Click="PrintDetailsBtn_Click"/>
            
            <Button Grid.Column="3" x:Name="CloseBtn" Content="❌ إغلاق" Width="100" Height="40" 
                    Background="#DC3545" Foreground="White" BorderThickness="0" 
                    FontSize="14" FontWeight="SemiBold" Click="CloseBtn_Click"/>
        </Grid>
    </Grid>
</Window>
