<Window x:Class="MedicalDevicesManager.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام إدارة الأجهزة الطبية المتكامل v6.0 - Medical Devices Management System Integrated" 
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="70"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>
        
        <!-- شريط العنوان -->
        <Border Grid.Row="0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#007BFF" Offset="0"/>
                    <GradientStop Color="#0056B3" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="30,0">
                    <TextBlock Text="🏥" FontSize="32" Foreground="White" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="نظام إدارة الأجهزة الطبية المتكامل" 
                                  FontSize="22" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="Medical Devices Management System v6.0 - Integrated Edition" 
                                  FontSize="12" Foreground="#B3D9FF"/>
                    </StackPanel>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,30,0">
                    <TextBlock Text="👤 المدير العام" 
                              Foreground="White" FontSize="14" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <TextBlock x:Name="DateTimeBlock" 
                              Foreground="#B3D9FF" FontSize="12" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- القائمة الجانبية -->
            <Border Grid.Column="0" Background="#343A40">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="0,20,0,0">
                        <Button x:Name="DashboardBtn" Content="🏠 لوحة التحكم" 
                                Background="#007BFF" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch" 
                                HorizontalContentAlignment="Left" FontSize="15" FontWeight="SemiBold"
                                Margin="0,0,0,2" Click="DashboardBtn_Click"/>
                        <Button x:Name="DevicesBtn" Content="🏥 الأجهزة الطبية" 
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch" 
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="DevicesBtn_Click"/>
                        <Button x:Name="InventoryBtn" Content="📦 إدارة المخزون" 
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch" 
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="InventoryBtn_Click"/>
                        <Button x:Name="SalesBtn" Content="💰 إدارة المبيعات" 
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch" 
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="SalesBtn_Click"/>
                        <Button x:Name="CustomersBtn" Content="👥 إدارة العملاء" 
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch" 
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="CustomersBtn_Click"/>
                        <Button x:Name="SuppliersBtn" Content="🏭 إدارة الموردين" 
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch" 
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="SuppliersBtn_Click"/>
                        <Button x:Name="ShipmentsBtn" Content="🚚 إدارة الشحنات" 
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch" 
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="ShipmentsBtn_Click"/>
                        <Button x:Name="MaintenanceBtn" Content="🛠️ الضمان والصيانة"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="MaintenanceBtn_Click"/>
                        <Button x:Name="InstallationsBtn" Content="🔧 إدارة التنصيبات"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="InstallationsBtn_Click"/>
                        <Button x:Name="SmartSystemBtn" Content="🧠 النظام الذكي"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="SmartSystemBtn_Click"/>
                        <Button x:Name="ReportsBtn" Content="📊 التقارير"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch" 
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="ReportsBtn_Click"/>
                        <Button x:Name="SettingsBtn" Content="⚙️ الإعدادات"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="SettingsBtn_Click"/>

                        <!-- فاصل -->
                        <Rectangle Height="1" Fill="#495057" Margin="20,10"/>

                        <!-- الوظائف المتقدمة -->
                        <TextBlock Text="الوظائف المتقدمة" Foreground="#ADB5BD" FontSize="12"
                                   FontWeight="SemiBold" Margin="25,10,0,5"/>

                        <Button x:Name="NotificationsBtn" Content="🔔 الإشعارات والتنبيهات"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="NotificationsBtn_Click"/>

                        <Button x:Name="BackupBtn" Content="💾 النسخ الاحتياطي"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="BackupBtn_Click"/>

                        <Button x:Name="ExportBtn" Content="📊 تصدير البيانات"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="ExportBtn_Click"/>

                        <Button x:Name="AdvancedInventoryBtn" Content="📦 إعدادات المخزون المتقدمة"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Padding="25,18" HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left" FontSize="15"
                                Margin="0,0,0,2" Click="AdvancedInventoryBtn_Click"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>
            
            <!-- منطقة المحتوى -->
            <Border Grid.Column="1" Background="#F8F9FA">
                <ScrollViewer x:Name="ContentArea" Padding="30" VerticalScrollBarVisibility="Auto"/>
            </Border>
        </Grid>
        
        <!-- شريط الحالة -->
        <Border Grid.Row="2" Background="#E9ECEF" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="جاهز - النظام يعمل بكفاءة" 
                          FontSize="12" Foreground="#6C757D" VerticalAlignment="Center" Margin="15,0"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,15,0">
                    <TextBlock Text="قاعدة البيانات: متصلة" FontSize="12" Foreground="#28A745" Margin="0,0,15,0"/>
                    <TextBlock Text="الإصدار: 6.0" FontSize="12" Foreground="#6C757D"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
