using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using System.Windows.Documents;
using System.Windows.Media;
using Microsoft.Win32;
using System.IO;
using System.Text;
using ClosedXML.Excel;
using System.Windows.Xps.Packaging;
using System.Windows.Xps;

namespace MedicalDevicesManager.Windows
{
    public partial class InventoryItemReportWindow : Window
    {
        private List<InventoryReportItem> _allItems;
        private List<InventoryReportItem> _filteredItems;

        public InventoryItemReportWindow()
        {
            InitializeComponent();
            LoadDataAsync();
        }

        private async void LoadDataAsync()
        {
            try
            {
                // تحميل الفئات
                var categories = await App.DatabaseContext.InventoryCategories.ToListAsync();
                CategoryFilterComboBox.Items.Add(new ComboBoxItem { Content = "الكل", Tag = null });
                foreach (var category in categories)
                {
                    CategoryFilterComboBox.Items.Add(new ComboBoxItem { Content = category.Name, Tag = category.Name });
                }
                CategoryFilterComboBox.SelectedIndex = 0;

                // تحميل بيانات المخزون
                await RefreshData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task RefreshData()
        {
            try
            {
                var inventoryItems = await App.DatabaseContext.InventoryItems.ToListAsync();

                _allItems = inventoryItems.Select(item => new InventoryReportItem
                {
                    Id = item.Id,
                    Name = item.Name,
                    CategoryName = item.Category ?? "غير محدد",
                    AvailableQuantity = item.CurrentStock,
                    MinimumStock = item.MinimumStock,
                    UnitPrice = item.UnitPrice,
                    TotalValue = item.CurrentStock * item.UnitPrice,
                    Status = item.Status,
                    Location = item.Location,
                    LastUpdated = item.LastUpdated
                }).ToList();

                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            _filteredItems = _allItems.ToList();

            // تطبيق فلتر الفئة
            if (CategoryFilterComboBox.SelectedItem is ComboBoxItem categoryItem && categoryItem.Tag != null)
            {
                var categoryName = categoryItem.Tag.ToString();
                _filteredItems = _filteredItems.Where(i => i.CategoryName == categoryName).ToList();
            }

            // تطبيق فلتر الحالة
            if (StatusFilterComboBox.SelectedItem is ComboBoxItem statusItem && statusItem.Content.ToString() != "الكل")
            {
                var status = statusItem.Content.ToString();
                _filteredItems = _filteredItems.Where(i => i.Status == status).ToList();
            }

            InventoryDataGrid.ItemsSource = _filteredItems;
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            var totalItems = _filteredItems.Count;
            var totalValue = _filteredItems.Sum(i => i.TotalValue);
            var lowStockItems = _filteredItems.Count(i => i.AvailableQuantity <= i.MinimumStock);

            TotalItemsText.Text = $"إجمالي الأصناف: {totalItems}";
            TotalValueText.Text = $"القيمة الإجمالية: {totalValue:C}";
            LowStockText.Text = $"أصناف تحت الحد الأدنى: {lowStockItems}";
        }

        private void CategoryFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allItems != null)
                ApplyFilters();
        }

        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allItems != null)
                ApplyFilters();
        }

        private async void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            await RefreshData();
        }

        private void PrintBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    var document = CreatePrintDocument();
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, "تقرير المخزون");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FlowDocument CreatePrintDocument()
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            // العنوان
            var title = new Paragraph(new Run("تقرير المخزون الشامل"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(title);

            // تاريخ التقرير
            var dateInfo = new Paragraph(new Run($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}"))
            {
                FontSize = 12,
                TextAlignment = TextAlignment.Right,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(dateInfo);

            // الجدول
            var table = new Table();
            table.CellSpacing = 0;
            table.BorderBrush = Brushes.Black;
            table.BorderThickness = new Thickness(1);

            // أعمدة الجدول
            table.Columns.Add(new TableColumn { Width = new GridLength(60) });
            table.Columns.Add(new TableColumn { Width = new GridLength(150) });
            table.Columns.Add(new TableColumn { Width = new GridLength(100) });
            table.Columns.Add(new TableColumn { Width = new GridLength(80) });
            table.Columns.Add(new TableColumn { Width = new GridLength(80) });
            table.Columns.Add(new TableColumn { Width = new GridLength(80) });
            table.Columns.Add(new TableColumn { Width = new GridLength(100) });

            // رأس الجدول
            var headerGroup = new TableRowGroup();
            var headerRow = new TableRow();
            headerRow.Background = Brushes.LightGray;

            var headers = new[] { "الرقم", "اسم الصنف", "الفئة", "الكمية", "الحد الأدنى", "السعر", "القيمة الإجمالية" };
            foreach (var header in headers)
            {
                var cell = new TableCell(new Paragraph(new Run(header)))
                {
                    BorderBrush = Brushes.Black,
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(5),
                    TextAlignment = TextAlignment.Center
                };
                headerRow.Cells.Add(cell);
            }
            headerGroup.Rows.Add(headerRow);
            table.RowGroups.Add(headerGroup);

            // بيانات الجدول
            var dataGroup = new TableRowGroup();
            foreach (var item in _filteredItems)
            {
                var row = new TableRow();
                var values = new[] 
                { 
                    item.Id.ToString(), 
                    item.Name, 
                    item.CategoryName, 
                    item.AvailableQuantity.ToString(),
                    item.MinimumStock.ToString(),
                    item.UnitPrice.ToString("C"),
                    item.TotalValue.ToString("C")
                };

                foreach (var value in values)
                {
                    var cell = new TableCell(new Paragraph(new Run(value)))
                    {
                        BorderBrush = Brushes.Black,
                        BorderThickness = new Thickness(1),
                        Padding = new Thickness(5),
                        TextAlignment = TextAlignment.Center
                    };
                    row.Cells.Add(cell);
                }
                dataGroup.Rows.Add(row);
            }
            table.RowGroups.Add(dataGroup);
            document.Blocks.Add(table);

            // الملخص
            var summary = new Paragraph()
            {
                Margin = new Thickness(0, 20, 0, 0),
                FontWeight = FontWeights.Bold
            };
            summary.Inlines.Add(new Run($"إجمالي الأصناف: {_filteredItems.Count}\n"));
            summary.Inlines.Add(new Run($"القيمة الإجمالية: {_filteredItems.Sum(i => i.TotalValue):C}\n"));
            summary.Inlines.Add(new Run($"أصناف تحت الحد الأدنى: {_filteredItems.Count(i => i.AvailableQuantity <= i.MinimumStock)}"));
            document.Blocks.Add(summary);

            return document;
        }

        private void ExportExcelBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                    DefaultExt = "xlsx",
                    FileName = $"تقرير_المخزون_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    if (saveDialog.FilterIndex == 1) // Excel
                    {
                        ExportToExcel(saveDialog.FileName);
                    }
                    else // CSV
                    {
                        ExportToCSV(saveDialog.FileName);
                    }

                    MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{saveDialog.FileName}", "نجح التصدير",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToExcel(string filePath)
        {
            try
            {
                using (var workbook = new ClosedXML.Excel.XLWorkbook())
                {
                    var worksheet = workbook.Worksheets.Add("تقرير المخزون");

                    // إعداد اتجاه النص من اليمين لليسار
                    worksheet.RightToLeft = true;

                    // العنوان الرئيسي
                    worksheet.Cell(1, 1).Value = "📦 تقرير المخزون التفصيلي";
                    worksheet.Cell(1, 1).Style.Font.Bold = true;
                    worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                    worksheet.Cell(1, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    worksheet.Range(1, 1, 1, 8).Merge();

                    // معلومات التقرير
                    worksheet.Cell(2, 1).Value = $"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}";
                    worksheet.Cell(3, 1).Value = $"إجمالي الأصناف: {_filteredItems.Count}";
                    worksheet.Cell(3, 3).Value = $"القيمة الإجمالية: {_filteredItems.Sum(i => i.TotalValue):C}";
                    worksheet.Cell(3, 5).Value = $"أصناف تحت الحد الأدنى: {_filteredItems.Count(i => i.AvailableQuantity <= i.MinimumStock)}";

                    // رؤوس الأعمدة
                    int headerRow = 5;
                    worksheet.Cell(headerRow, 1).Value = "الرقم";
                    worksheet.Cell(headerRow, 2).Value = "اسم الصنف";
                    worksheet.Cell(headerRow, 3).Value = "الفئة";
                    worksheet.Cell(headerRow, 4).Value = "الكمية المتاحة";
                    worksheet.Cell(headerRow, 5).Value = "الحد الأدنى";
                    worksheet.Cell(headerRow, 6).Value = "سعر الوحدة";
                    worksheet.Cell(headerRow, 7).Value = "القيمة الإجمالية";
                    worksheet.Cell(headerRow, 8).Value = "الحالة";

                    // تنسيق رؤوس الأعمدة
                    var headerRange = worksheet.Range(headerRow, 1, headerRow, 8);
                    headerRange.Style.Font.Bold = true;
                    headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;
                    headerRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                    // البيانات
                    int currentRow = headerRow + 1;
                    foreach (var item in _filteredItems)
                    {
                        worksheet.Cell(currentRow, 1).Value = item.Id;
                        worksheet.Cell(currentRow, 2).Value = item.Name;
                        worksheet.Cell(currentRow, 3).Value = item.CategoryName;
                        worksheet.Cell(currentRow, 4).Value = item.AvailableQuantity;
                        worksheet.Cell(currentRow, 5).Value = item.MinimumStock;
                        worksheet.Cell(currentRow, 6).Value = item.UnitPrice;
                        worksheet.Cell(currentRow, 7).Value = item.TotalValue;
                        worksheet.Cell(currentRow, 8).Value = item.Status;

                        // تلوين الصفوف حسب الحالة
                        if (item.AvailableQuantity <= item.MinimumStock)
                        {
                            worksheet.Range(currentRow, 1, currentRow, 8).Style.Fill.BackgroundColor = XLColor.LightPink;
                        }
                        else if (item.Status == "متاح")
                        {
                            worksheet.Range(currentRow, 1, currentRow, 8).Style.Fill.BackgroundColor = XLColor.LightGreen;
                        }

                        currentRow++;
                    }

                    // تنسيق الأعمدة
                    worksheet.Columns().AdjustToContents();
                    worksheet.Column(2).Width = 25; // اسم الصنف
                    worksheet.Column(3).Width = 15; // الفئة

                    // إضافة حدود للجدول
                    var dataRange = worksheet.Range(headerRow, 1, currentRow - 1, 8);
                    dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                    dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                    workbook.SaveAs(filePath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء ملف Excel: {ex.Message}");
            }
        }

        private void ExportToCSV(string filePath)
        {
            try
            {
                using (var writer = new System.IO.StreamWriter(filePath, false, System.Text.Encoding.UTF8))
                {
                    // كتابة العنوان
                    writer.WriteLine("تقرير المخزون التفصيلي");
                    writer.WriteLine($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}");
                    writer.WriteLine($"إجمالي الأصناف: {_filteredItems.Count}");
                    writer.WriteLine($"القيمة الإجمالية: {_filteredItems.Sum(i => i.TotalValue):C}");
                    writer.WriteLine($"أصناف تحت الحد الأدنى: {_filteredItems.Count(i => i.AvailableQuantity <= i.MinimumStock)}");
                    writer.WriteLine(); // سطر فارغ

                    // كتابة رؤوس الأعمدة
                    writer.WriteLine("الرقم,اسم الصنف,الفئة,الكمية المتاحة,الحد الأدنى,سعر الوحدة,القيمة الإجمالية,الحالة");

                    // كتابة البيانات
                    foreach (var item in _filteredItems)
                    {
                        writer.WriteLine($"{item.Id},\"{item.Name}\",\"{item.CategoryName}\",{item.AvailableQuantity},{item.MinimumStock},{item.UnitPrice},{item.TotalValue},\"{item.Status}\"");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء ملف CSV: {ex.Message}");
            }
        }

        private void ExportPdfBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"تقرير_المخزون_{DateTime.Now:yyyyMMdd_HHmmss}.pdf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    ExportToPDF(saveDialog.FileName);
                    MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{saveDialog.FileName}", "نجح التصدير",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToPDF(string filePath)
        {
            try
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                var document = CreatePrintDocument();

                // حفظ كـ XPS أولاً ثم تحويل لـ PDF
                var xpsPath = System.IO.Path.ChangeExtension(filePath, ".xps");

                using (var xpsDocument = new XpsDocument(xpsPath, System.IO.FileAccess.Write))
                {
                    var xpsWriter = XpsDocument.CreateXpsDocumentWriter(xpsDocument);
                    xpsWriter.Write(((System.Windows.Documents.IDocumentPaginatorSource)document).DocumentPaginator);
                }

                // تحويل XPS إلى PDF باستخدام Print to PDF
                ConvertXpsToPdf(xpsPath, filePath);

                // حذف ملف XPS المؤقت
                if (System.IO.File.Exists(xpsPath))
                {
                    System.IO.File.Delete(xpsPath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء ملف PDF: {ex.Message}");
            }
        }

        private void ConvertXpsToPdf(string xpsPath, string pdfPath)
        {
            try
            {
                // استخدام Microsoft Print to PDF أو طريقة بديلة
                var printDialog = new System.Windows.Controls.PrintDialog();

                // البحث عن طابعة PDF
                var pdfPrinter = System.Drawing.Printing.PrinterSettings.InstalledPrinters
                    .Cast<string>()
                    .FirstOrDefault(p => p.Contains("PDF") || p.Contains("pdf"));

                if (!string.IsNullOrEmpty(pdfPrinter))
                {
                    printDialog.PrintQueue = new System.Printing.PrintQueue(new System.Printing.PrintServer(), pdfPrinter);

                    // طباعة المستند
                    var document = CreatePrintDocument();
                    printDialog.PrintDocument(((System.Windows.Documents.IDocumentPaginatorSource)document).DocumentPaginator, "تقرير المخزون");
                }
                else
                {
                    // إذا لم توجد طابعة PDF، استخدم طريقة بديلة
                    CreatePdfAlternative(pdfPath);
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل التحويل، استخدم طريقة بديلة
                CreatePdfAlternative(pdfPath);
            }
        }

        private void CreatePdfAlternative(string pdfPath)
        {
            try
            {
                // إنشاء PDF بسيط باستخدام HTML
                var htmlContent = GenerateHtmlReport();
                var htmlPath = System.IO.Path.ChangeExtension(pdfPath, ".html");

                System.IO.File.WriteAllText(htmlPath, htmlContent, System.Text.Encoding.UTF8);

                // إشعار المستخدم بأن HTML تم إنشاؤه
                MessageBox.Show($"تم إنشاء تقرير HTML بدلاً من PDF:\n{htmlPath}\nيمكنك فتحه في المتصفح وطباعته كـ PDF",
                              "تم إنشاء HTML", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء التقرير البديل: {ex.Message}");
            }
        }

        private string GenerateHtmlReport()
        {
            var html = new System.Text.StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl' lang='ar'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine("<title>تقرير المخزون</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: 'Segoe UI', Tahoma, Arial, sans-serif; margin: 20px; }");
            html.AppendLine("h1 { text-align: center; color: #2c3e50; }");
            html.AppendLine("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }");
            html.AppendLine("th { background-color: #3498db; color: white; }");
            html.AppendLine(".low-stock { background-color: #ffebee; }");
            html.AppendLine(".available { background-color: #e8f5e8; }");
            html.AppendLine(".summary { background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }");
            html.AppendLine("@media print { body { margin: 0; } }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // العنوان
            html.AppendLine("<h1>📦 تقرير المخزون التفصيلي</h1>");

            // الملخص
            html.AppendLine("<div class='summary'>");
            html.AppendLine($"<p><strong>تاريخ التقرير:</strong> {DateTime.Now:dd/MM/yyyy HH:mm}</p>");
            html.AppendLine($"<p><strong>إجمالي الأصناف:</strong> {_filteredItems.Count}</p>");
            html.AppendLine($"<p><strong>القيمة الإجمالية:</strong> {_filteredItems.Sum(i => i.TotalValue):C}</p>");
            html.AppendLine($"<p><strong>أصناف تحت الحد الأدنى:</strong> {_filteredItems.Count(i => i.AvailableQuantity <= i.MinimumStock)}</p>");
            html.AppendLine("</div>");

            // الجدول
            html.AppendLine("<table>");
            html.AppendLine("<thead>");
            html.AppendLine("<tr>");
            html.AppendLine("<th>الرقم</th>");
            html.AppendLine("<th>اسم الصنف</th>");
            html.AppendLine("<th>الفئة</th>");
            html.AppendLine("<th>الكمية المتاحة</th>");
            html.AppendLine("<th>الحد الأدنى</th>");
            html.AppendLine("<th>سعر الوحدة</th>");
            html.AppendLine("<th>القيمة الإجمالية</th>");
            html.AppendLine("<th>الحالة</th>");
            html.AppendLine("</tr>");
            html.AppendLine("</thead>");
            html.AppendLine("<tbody>");

            foreach (var item in _filteredItems)
            {
                var rowClass = item.AvailableQuantity <= item.MinimumStock ? "low-stock" :
                              item.Status == "متاح" ? "available" : "";

                html.AppendLine($"<tr class='{rowClass}'>");
                html.AppendLine($"<td>{item.Id}</td>");
                html.AppendLine($"<td>{item.Name}</td>");
                html.AppendLine($"<td>{item.CategoryName}</td>");
                html.AppendLine($"<td>{item.AvailableQuantity}</td>");
                html.AppendLine($"<td>{item.MinimumStock}</td>");
                html.AppendLine($"<td>{item.UnitPrice:C}</td>");
                html.AppendLine($"<td>{item.TotalValue:C}</td>");
                html.AppendLine($"<td>{item.Status}</td>");
                html.AppendLine("</tr>");
            }

            html.AppendLine("</tbody>");
            html.AppendLine("</table>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }
    }

    public class InventoryReportItem
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string CategoryName { get; set; }
        public int AvailableQuantity { get; set; }
        public int MinimumStock { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue { get; set; }
        public string Status { get; set; }
        public string Location { get; set; }
        public DateTime LastUpdated { get; set; }
    }
}
