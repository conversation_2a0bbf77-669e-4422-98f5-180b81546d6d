﻿#pragma checksum "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BAD6B620132F104FA3CBAB8B148A468B0F9EA850"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MedicalDevicesManager.Windows {
    
    
    /// <summary>
    /// ManageSerialNumbersWindow
    /// </summary>
    public partial class ManageSerialNumbersWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceModelTextBlock;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddSerialBtn;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditSerialBtn;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteSerialBtn;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseBtn;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SerialNumbersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border EditPanel;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EditPanelTitle;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SerialNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ComponentNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ComponentTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocationTextBox;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker InstallationDatePicker;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveBtn;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MedicalDevicesManager;component/windows/manageserialnumberswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DeviceNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DeviceModelTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.AddSerialBtn = ((System.Windows.Controls.Button)(target));
            
            #line 54 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
            this.AddSerialBtn.Click += new System.Windows.RoutedEventHandler(this.AddSerialBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.EditSerialBtn = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
            this.EditSerialBtn.Click += new System.Windows.RoutedEventHandler(this.EditSerialBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DeleteSerialBtn = ((System.Windows.Controls.Button)(target));
            
            #line 62 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
            this.DeleteSerialBtn.Click += new System.Windows.RoutedEventHandler(this.DeleteSerialBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CloseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
            this.CloseBtn.Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SerialNumbersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 74 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
            this.SerialNumbersDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SerialNumbersDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.EditPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.EditPanelTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.SerialNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.ComponentNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ComponentTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.LocationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.InstallationDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 16:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.SaveBtn = ((System.Windows.Controls.Button)(target));
            
            #line 209 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
            this.SaveBtn.Click += new System.Windows.RoutedEventHandler(this.SaveBtn_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.CancelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 212 "..\..\..\..\Windows\ManageSerialNumbersWindow.xaml"
            this.CancelBtn.Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

